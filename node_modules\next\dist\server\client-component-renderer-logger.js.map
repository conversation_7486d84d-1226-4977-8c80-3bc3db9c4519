{"version": 3, "sources": ["../../src/server/client-component-renderer-logger.ts"], "names": ["getClientComponentLoaderMetrics", "wrapClientComponentLoader", "clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "ComponentMod", "globalThis", "__next_app__", "require", "args", "performance", "now", "startTime", "loadChunk", "options", "metrics", "undefined", "reset"], "mappings": "AAAA,oDAAoD;;;;;;;;;;;;;;;;IAoCpCA,+BAA+B;eAA/BA;;IA/BAC,yBAAyB;eAAzBA;;;AAJhB,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAExB,SAASH,0BAA0BI,YAAiB;IACzD,IAAI,CAAE,CAAA,iBAAiBC,UAAS,GAAI;QAClC,OAAOD,aAAaE,YAAY;IAClC;IAEA,OAAO;QACLC,SAAS,CAAC,GAAGC;YACX,IAAIP,6BAA6B,GAAG;gBAClCA,2BAA2BQ,YAAYC,GAAG;YAC5C;YAEA,MAAMC,YAAYF,YAAYC,GAAG;YACjC,IAAI;gBACFP,4BAA4B;gBAC5B,OAAOC,aAAaE,YAAY,CAACC,OAAO,IAAIC;YAC9C,SAAU;gBACRN,4BAA4BO,YAAYC,GAAG,KAAKC;YAClD;QACF;QACAC,WAAW,CAAC,GAAGJ;YACb,MAAMG,YAAYF,YAAYC,GAAG;YACjC,IAAI;gBACFP,4BAA4B;gBAC5B,OAAOC,aAAaE,YAAY,CAACM,SAAS,IAAIJ;YAChD,SAAU;gBACRN,4BAA4BO,YAAYC,GAAG,KAAKC;YAClD;QACF;IACF;AACF;AAEO,SAASZ,gCACdc,UAA+B,CAAC,CAAC;IAEjC,MAAMC,UACJb,6BAA6B,IACzBc,YACA;QACEd;QACAC;QACAC;IACF;IAEN,IAAIU,QAAQG,KAAK,EAAE;QACjBf,2BAA2B;QAC3BC,2BAA2B;QAC3BC,2BAA2B;IAC7B;IAEA,OAAOW;AACT"}