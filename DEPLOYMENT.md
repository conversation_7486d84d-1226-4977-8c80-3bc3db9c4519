# VisualVibe Deployment Guide

This guide will help you deploy VisualVibe to production.

## 🚀 Quick Deployment (Vercel + Supabase)

### 1. Set up Supabase

1. **Create a Supabase project**:
   - Go to [supabase.com](https://supabase.com)
   - Click "New Project"
   - Choose your organization and enter project details
   - Wait for the project to be created

2. **Set up the database**:
   - Go to SQL Editor in your Supabase dashboard
   - Copy the entire contents of `database/schema.sql`
   - Paste and execute the SQL
   - Verify all tables are created successfully

3. **Configure Storage**:
   - Go to Storage in your Supabase dashboard
   - The `images` bucket should be created automatically
   - Verify the bucket policies are active

4. **Get your credentials**:
   - Go to Settings > API
   - Copy your Project URL and anon public key

### 2. Deploy to Vercel

1. **Push to GitHub**:
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin <your-github-repo-url>
   git push -u origin main
   ```

2. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Vercel will auto-detect it's a Next.js project

3. **Add Environment Variables**:
   In your Vercel project settings, add these environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Deploy**:
   - Click "Deploy"
   - Wait for the build to complete
   - Your app will be live at `https://your-project.vercel.app`

### 3. Configure Custom Domain (Optional)

1. **Add domain in Vercel**:
   - Go to your project settings
   - Click "Domains"
   - Add your custom domain

2. **Update DNS**:
   - Add CNAME record pointing to `cname.vercel-dns.com`
   - Or add A record pointing to Vercel's IP

## 🔧 Advanced Configuration

### Instagram API Integration

1. **Create Facebook Developer App**:
   - Go to [developers.facebook.com](https://developers.facebook.com)
   - Create a new app
   - Add Instagram Basic Display product

2. **Configure Instagram Basic Display**:
   - Add redirect URI: `https://yourdomain.com/auth/instagram/callback`
   - Add deauthorize callback URL
   - Add data deletion request URL

3. **Add environment variables**:
   ```
   INSTAGRAM_CLIENT_ID=your_instagram_app_id
   INSTAGRAM_CLIENT_SECRET=your_instagram_app_secret
   INSTAGRAM_REDIRECT_URI=https://yourdomain.com/auth/instagram/callback
   ```

### Stripe Payment Integration

1. **Create Stripe Account**:
   - Go to [stripe.com](https://stripe.com)
   - Create an account and verify your business

2. **Set up Products**:
   - Create products for Premium ($9/month) and Pro ($29/month)
   - Note down the price IDs

3. **Configure Webhooks**:
   - Add webhook endpoint: `https://yourdomain.com/api/webhooks/stripe`
   - Select events: `customer.subscription.created`, `customer.subscription.updated`, `customer.subscription.deleted`

4. **Add environment variables**:
   ```
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

## 🌍 Alternative Deployment Options

### Netlify

1. **Build settings**:
   - Build command: `npm run build`
   - Publish directory: `.next`

2. **Environment variables**:
   Add the same environment variables as Vercel

### Railway

1. **Connect GitHub repo**
2. **Add environment variables**
3. **Deploy automatically**

### DigitalOcean App Platform

1. **Create new app**
2. **Connect GitHub repository**
3. **Configure build settings**:
   - Build command: `npm run build`
   - Run command: `npm start`

## 📊 Monitoring & Analytics

### Set up Sentry (Error Monitoring)

1. **Create Sentry project**:
   - Go to [sentry.io](https://sentry.io)
   - Create a new Next.js project

2. **Install Sentry**:
   ```bash
   npm install @sentry/nextjs
   ```

3. **Configure Sentry**:
   Add to `next.config.js`:
   ```javascript
   const { withSentryConfig } = require('@sentry/nextjs');
   
   module.exports = withSentryConfig(nextConfig, {
     silent: true,
     org: "your-org",
     project: "your-project",
   });
   ```

### Set up Analytics

1. **Google Analytics**:
   - Create GA4 property
   - Add tracking ID to environment variables
   - Implement tracking in your app

2. **Vercel Analytics**:
   - Enable in Vercel dashboard
   - No additional configuration needed

## 🔒 Security Checklist

- [ ] Environment variables are properly set
- [ ] Supabase RLS policies are enabled
- [ ] HTTPS is enforced
- [ ] CORS is properly configured
- [ ] Rate limiting is implemented
- [ ] Input validation is in place
- [ ] File upload restrictions are set

## 🚨 Troubleshooting

### Common Issues

1. **Build fails with TypeScript errors**:
   ```bash
   npm run type-check
   ```
   Fix any TypeScript errors before deploying

2. **Supabase connection fails**:
   - Verify environment variables are correct
   - Check if Supabase project is active
   - Ensure RLS policies allow access

3. **Images not uploading**:
   - Check Supabase storage policies
   - Verify bucket exists and is public
   - Check file size limits

4. **Authentication not working**:
   - Verify Supabase auth settings
   - Check redirect URLs
   - Ensure email templates are configured

### Performance Optimization

1. **Enable caching**:
   ```javascript
   // next.config.js
   module.exports = {
     images: {
       domains: ['your-supabase-url'],
     },
     experimental: {
       optimizeCss: true,
     },
   }
   ```

2. **Optimize images**:
   - Use Next.js Image component
   - Enable image optimization in Supabase
   - Implement lazy loading

3. **Database optimization**:
   - Add indexes for frequently queried columns
   - Use database functions for complex queries
   - Implement pagination for large datasets

## 📈 Scaling Considerations

### Database Scaling
- Monitor Supabase usage and upgrade plan as needed
- Consider read replicas for high-traffic scenarios
- Implement database connection pooling

### CDN and Caching
- Use Vercel's Edge Network
- Implement Redis for session storage
- Cache API responses appropriately

### Monitoring
- Set up uptime monitoring
- Monitor database performance
- Track user analytics and conversion rates

## 🎯 Go-Live Checklist

- [ ] All environment variables configured
- [ ] Database schema deployed
- [ ] SSL certificate active
- [ ] Custom domain configured
- [ ] Error monitoring set up
- [ ] Analytics tracking implemented
- [ ] Backup strategy in place
- [ ] Performance testing completed
- [ ] Security audit passed
- [ ] Documentation updated

## 📞 Support

If you encounter issues during deployment:

1. Check the [troubleshooting section](#troubleshooting)
2. Review Vercel and Supabase documentation
3. Create an issue in the GitHub repository
4. Contact <NAME_EMAIL>

---

🎉 **Congratulations!** Your VisualVibe app should now be live and ready for users!
