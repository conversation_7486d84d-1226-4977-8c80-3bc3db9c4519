{"version": 3, "sources": ["../../../src/server/app-render/encryption.ts"], "names": ["decryptActionBoundArgs", "encryptActionBoundArgs", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "getActionEncryptionKey", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "decrypt", "stringToUint8Array", "startsWith", "length", "encodeActionBoundArg", "undefined", "randomBytes", "Uint8Array", "crypto", "getRandomValues", "arrayBufferToString", "buffer", "encrypted", "encrypt", "encode", "btoa", "args", "clientReferenceManifestSingleton", "getClientReferenceManifestSingleton", "serialized", "streamToString", "renderToReadableStream", "clientModules", "decryped", "deserialized", "createFromReadableStream", "ReadableStream", "start", "controller", "enqueue", "close", "ssrManifest", "moduleLoading", "moduleMap", "serverModuleMap", "getServerModuleMap", "transformed", "decodeReply", "encodeReply"], "mappings": "AAAA,oDAAoD;;;;;;;;;;;;;;;IA4F9BA,sBAAsB;eAAtBA;;IAjBAC,sBAAsB;eAAtBA;;;QA1Ef;4BAMA;4BAKA;sCAEwB;iCASxB;AAEP,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,uCAAsB;IACxC,IAAI,OAAOD,QAAQ,aAAa;QAC9B,MAAM,IAAIE,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKL;IAC7B,MAAMM,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYb,YAAYc,MAAM,CAClC,MAAMC,IAAAA,wBAAO,EAACV,KAAKW,IAAAA,mCAAkB,EAACN,UAAUM,IAAAA,mCAAkB,EAACJ;IAGrE,IAAI,CAACC,UAAUI,UAAU,CAACd,WAAW;QACnC,MAAM,IAAII,MAAM;IAClB;IAEA,OAAOM,UAAUF,KAAK,CAACR,SAASe,MAAM;AACxC;AAEA,eAAeC,qBAAqBhB,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,uCAAsB;IACxC,IAAID,QAAQe,WAAW;QACrB,MAAM,IAAIb,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,6BAA6B;IAC7B,MAAMc,cAAc,IAAIC,WAAW;IACnCC,OAAOC,eAAe,CAACH;IACvB,MAAMX,UAAUe,IAAAA,oCAAmB,EAACJ,YAAYK,MAAM;IAEtD,MAAMC,YAAY,MAAMC,IAAAA,wBAAO,EAC7BvB,KACAgB,aACAvB,YAAY+B,MAAM,CAAC1B,WAAWC;IAGhC,OAAO0B,KAAKpB,UAAUe,IAAAA,oCAAmB,EAACE;AAC5C;AAGO,eAAe9B,uBAAuBM,QAAgB,EAAE4B,IAAW;IACxE,MAAMC,mCAAmCC,IAAAA,oDAAmC;IAE5E,oDAAoD;IACpD,MAAMC,aAAa,MAAMC,IAAAA,oCAAc,EACrCC,IAAAA,kCAAsB,EAACL,MAAMC,iCAAiCK,aAAa;IAG7E,gEAAgE;IAChE,gFAAgF;IAChF,iBAAiB;IACjB,MAAMV,YAAY,MAAMR,qBAAqBhB,UAAU+B;IAEvD,OAAOP;AACT;AAGO,eAAe/B,uBACpBO,QAAgB,EAChBwB,SAA0B;IAE1B,gEAAgE;IAChE,MAAMW,WAAW,MAAMpC,qBAAqBC,UAAU,MAAMwB;IAE5D,wDAAwD;IACxD,MAAMY,eAAe,MAAMC,IAAAA,oCAAwB,EACjD,IAAIC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAC9C,YAAY+B,MAAM,CAACS;YACtCK,WAAWE,KAAK;QAClB;IACF,IACA;QACEC,aAAa;YACX,0EAA0E;YAC1E,uEAAuE;YACvE,8BAA8B;YAC9B,+CAA+C;YAC/CC,eAAe,CAAC;YAChBC,WAAW,CAAC;QACd;IACF;IAGF,oEAAoE;IACpE,MAAMC,kBAAkBC,IAAAA,mCAAkB;IAC1C,MAAMC,cAAc,MAAMC,IAAAA,uBAAW,EACnC,MAAMC,IAAAA,uBAAW,EAACd,eAClBU;IAGF,OAAOE;AACT"}