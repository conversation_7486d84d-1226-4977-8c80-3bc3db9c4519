'use client'

import { useFeed } from '@/contexts/FeedContext'
import { PostSlot } from './PostSlot'
import { DragDropPost } from './DragDropPost'

export function FeedGrid() {
  const { posts, gridSize, getPostAtPosition } = useFeed()
  
  const gridCols = gridSize === '3x3' ? 3 : 6
  const totalSlots = gridCols * gridCols

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div 
        className={`
          grid gap-2 w-full aspect-square
          ${gridSize === '3x3' ? 'grid-cols-3' : 'grid-cols-6'}
        `}
      >
        {Array.from({ length: totalSlots }, (_, index) => {
          const post = getPostAtPosition(index)
          
          return (
            <PostSlot
              key={index}
              position={index}
              isEmpty={!post}
            >
              {post && (
                <DragDropPost
                  post={post}
                  position={index}
                />
              )}
            </PostSlot>
          )
        })}
      </div>
      
      {/* Grid Info */}
      <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
        <span>{posts.length} of {totalSlots} slots filled</span>
        <span>{gridSize} grid</span>
      </div>
    </div>
  )
}
