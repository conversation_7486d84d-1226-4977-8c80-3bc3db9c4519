"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dnd-html5-backend";
exports.ids = ["vendor-chunks/react-dnd-html5-backend"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFirefox: () => (/* binding */ isFirefox),\n/* harmony export */   isSafari: () => (/* binding */ isSafari)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/js_utils.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js\");\n\nconst isFirefox = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoize)(()=>/firefox/i.test(navigator.userAgent)\n);\nconst isSafari = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoize)(()=>Boolean(window.safari)\n);\n\n//# sourceMappingURL=BrowserDetector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9Ccm93c2VyRGV0ZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBQ3ZDLGtCQUFrQiwyREFBTztBQUNoQztBQUNPLGlCQUFpQiwyREFBTztBQUMvQjs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvQnJvd3NlckRldGVjdG9yLmpzP2M4YTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWVtb2l6ZSB9IGZyb20gJy4vdXRpbHMvanNfdXRpbHMuanMnO1xuZXhwb3J0IGNvbnN0IGlzRmlyZWZveCA9IG1lbW9pemUoKCk9Pi9maXJlZm94L2kudGVzdChuYXZpZ2F0b3IudXNlckFnZW50KVxuKTtcbmV4cG9ydCBjb25zdCBpc1NhZmFyaSA9IG1lbW9pemUoKCk9PkJvb2xlYW4od2luZG93LnNhZmFyaSlcbik7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUJyb3dzZXJEZXRlY3Rvci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnterLeaveCounter: () => (/* binding */ EnterLeaveCounter)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/js_utils.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js\");\n\nclass EnterLeaveCounter {\n    enter(enteringNode) {\n        const previousLength = this.entered.length;\n        const isNodeEntered = (node)=>this.isNodeInDocument(node) && (!node.contains || node.contains(enteringNode))\n        ;\n        this.entered = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.union)(this.entered.filter(isNodeEntered), [\n            enteringNode\n        ]);\n        return previousLength === 0 && this.entered.length > 0;\n    }\n    leave(leavingNode) {\n        const previousLength = this.entered.length;\n        this.entered = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.without)(this.entered.filter(this.isNodeInDocument), leavingNode);\n        return previousLength > 0 && this.entered.length === 0;\n    }\n    reset() {\n        this.entered = [];\n    }\n    constructor(isNodeInDocument){\n        this.entered = [];\n        this.isNodeInDocument = isNodeInDocument;\n    }\n}\n\n//# sourceMappingURL=EnterLeaveCounter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9FbnRlckxlYXZlQ291bnRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUM5QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHlEQUFLO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwyREFBTztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvRW50ZXJMZWF2ZUNvdW50ZXIuanM/YjZkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1bmlvbiwgd2l0aG91dCB9IGZyb20gJy4vdXRpbHMvanNfdXRpbHMuanMnO1xuZXhwb3J0IGNsYXNzIEVudGVyTGVhdmVDb3VudGVyIHtcbiAgICBlbnRlcihlbnRlcmluZ05vZGUpIHtcbiAgICAgICAgY29uc3QgcHJldmlvdXNMZW5ndGggPSB0aGlzLmVudGVyZWQubGVuZ3RoO1xuICAgICAgICBjb25zdCBpc05vZGVFbnRlcmVkID0gKG5vZGUpPT50aGlzLmlzTm9kZUluRG9jdW1lbnQobm9kZSkgJiYgKCFub2RlLmNvbnRhaW5zIHx8IG5vZGUuY29udGFpbnMoZW50ZXJpbmdOb2RlKSlcbiAgICAgICAgO1xuICAgICAgICB0aGlzLmVudGVyZWQgPSB1bmlvbih0aGlzLmVudGVyZWQuZmlsdGVyKGlzTm9kZUVudGVyZWQpLCBbXG4gICAgICAgICAgICBlbnRlcmluZ05vZGVcbiAgICAgICAgXSk7XG4gICAgICAgIHJldHVybiBwcmV2aW91c0xlbmd0aCA9PT0gMCAmJiB0aGlzLmVudGVyZWQubGVuZ3RoID4gMDtcbiAgICB9XG4gICAgbGVhdmUobGVhdmluZ05vZGUpIHtcbiAgICAgICAgY29uc3QgcHJldmlvdXNMZW5ndGggPSB0aGlzLmVudGVyZWQubGVuZ3RoO1xuICAgICAgICB0aGlzLmVudGVyZWQgPSB3aXRob3V0KHRoaXMuZW50ZXJlZC5maWx0ZXIodGhpcy5pc05vZGVJbkRvY3VtZW50KSwgbGVhdmluZ05vZGUpO1xuICAgICAgICByZXR1cm4gcHJldmlvdXNMZW5ndGggPiAwICYmIHRoaXMuZW50ZXJlZC5sZW5ndGggPT09IDA7XG4gICAgfVxuICAgIHJlc2V0KCkge1xuICAgICAgICB0aGlzLmVudGVyZWQgPSBbXTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoaXNOb2RlSW5Eb2N1bWVudCl7XG4gICAgICAgIHRoaXMuZW50ZXJlZCA9IFtdO1xuICAgICAgICB0aGlzLmlzTm9kZUluRG9jdW1lbnQgPSBpc05vZGVJbkRvY3VtZW50O1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9RW50ZXJMZWF2ZUNvdW50ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTML5BackendImpl: () => (/* binding */ HTML5BackendImpl)\n/* harmony export */ });\n/* harmony import */ var _EnterLeaveCounter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnterLeaveCounter.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js\");\n/* harmony import */ var _NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeDragSources/index.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js\");\n/* harmony import */ var _NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NativeTypes.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\");\n/* harmony import */ var _OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OffsetUtils.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js\");\n/* harmony import */ var _OptionsReader_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OptionsReader.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/OptionsReader.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\n\n\nclass HTML5BackendImpl {\n    /**\n\t * Generate profiling statistics for the HTML5Backend.\n\t */ profile() {\n        var ref, ref1;\n        return {\n            sourcePreviewNodes: this.sourcePreviewNodes.size,\n            sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n            sourceNodeOptions: this.sourceNodeOptions.size,\n            sourceNodes: this.sourceNodes.size,\n            dragStartSourceIds: ((ref = this.dragStartSourceIds) === null || ref === void 0 ? void 0 : ref.length) || 0,\n            dropTargetIds: this.dropTargetIds.length,\n            dragEnterTargetIds: this.dragEnterTargetIds.length,\n            dragOverTargetIds: ((ref1 = this.dragOverTargetIds) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n        };\n    }\n    // public for test\n    get window() {\n        return this.options.window;\n    }\n    get document() {\n        return this.options.document;\n    }\n    /**\n\t * Get the root element to use for event subscriptions\n\t */ get rootElement() {\n        return this.options.rootElement;\n    }\n    setup() {\n        const root = this.rootElement;\n        if (root === undefined) {\n            return;\n        }\n        if (root.__isReactDndBackendSetUp) {\n            throw new Error('Cannot have two HTML5 backends at the same time.');\n        }\n        root.__isReactDndBackendSetUp = true;\n        this.addEventListeners(root);\n    }\n    teardown() {\n        const root = this.rootElement;\n        if (root === undefined) {\n            return;\n        }\n        root.__isReactDndBackendSetUp = false;\n        this.removeEventListeners(this.rootElement);\n        this.clearCurrentDragSourceNode();\n        if (this.asyncEndDragFrameId) {\n            var ref;\n            (ref = this.window) === null || ref === void 0 ? void 0 : ref.cancelAnimationFrame(this.asyncEndDragFrameId);\n        }\n    }\n    connectDragPreview(sourceId, node, options) {\n        this.sourcePreviewNodeOptions.set(sourceId, options);\n        this.sourcePreviewNodes.set(sourceId, node);\n        return ()=>{\n            this.sourcePreviewNodes.delete(sourceId);\n            this.sourcePreviewNodeOptions.delete(sourceId);\n        };\n    }\n    connectDragSource(sourceId, node, options) {\n        this.sourceNodes.set(sourceId, node);\n        this.sourceNodeOptions.set(sourceId, options);\n        const handleDragStart = (e)=>this.handleDragStart(e, sourceId)\n        ;\n        const handleSelectStart = (e)=>this.handleSelectStart(e)\n        ;\n        node.setAttribute('draggable', 'true');\n        node.addEventListener('dragstart', handleDragStart);\n        node.addEventListener('selectstart', handleSelectStart);\n        return ()=>{\n            this.sourceNodes.delete(sourceId);\n            this.sourceNodeOptions.delete(sourceId);\n            node.removeEventListener('dragstart', handleDragStart);\n            node.removeEventListener('selectstart', handleSelectStart);\n            node.setAttribute('draggable', 'false');\n        };\n    }\n    connectDropTarget(targetId, node) {\n        const handleDragEnter = (e)=>this.handleDragEnter(e, targetId)\n        ;\n        const handleDragOver = (e)=>this.handleDragOver(e, targetId)\n        ;\n        const handleDrop = (e)=>this.handleDrop(e, targetId)\n        ;\n        node.addEventListener('dragenter', handleDragEnter);\n        node.addEventListener('dragover', handleDragOver);\n        node.addEventListener('drop', handleDrop);\n        return ()=>{\n            node.removeEventListener('dragenter', handleDragEnter);\n            node.removeEventListener('dragover', handleDragOver);\n            node.removeEventListener('drop', handleDrop);\n        };\n    }\n    addEventListeners(target) {\n        // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n        if (!target.addEventListener) {\n            return;\n        }\n        target.addEventListener('dragstart', this.handleTopDragStart);\n        target.addEventListener('dragstart', this.handleTopDragStartCapture, true);\n        target.addEventListener('dragend', this.handleTopDragEndCapture, true);\n        target.addEventListener('dragenter', this.handleTopDragEnter);\n        target.addEventListener('dragenter', this.handleTopDragEnterCapture, true);\n        target.addEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n        target.addEventListener('dragover', this.handleTopDragOver);\n        target.addEventListener('dragover', this.handleTopDragOverCapture, true);\n        target.addEventListener('drop', this.handleTopDrop);\n        target.addEventListener('drop', this.handleTopDropCapture, true);\n    }\n    removeEventListeners(target) {\n        // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n        if (!target.removeEventListener) {\n            return;\n        }\n        target.removeEventListener('dragstart', this.handleTopDragStart);\n        target.removeEventListener('dragstart', this.handleTopDragStartCapture, true);\n        target.removeEventListener('dragend', this.handleTopDragEndCapture, true);\n        target.removeEventListener('dragenter', this.handleTopDragEnter);\n        target.removeEventListener('dragenter', this.handleTopDragEnterCapture, true);\n        target.removeEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n        target.removeEventListener('dragover', this.handleTopDragOver);\n        target.removeEventListener('dragover', this.handleTopDragOverCapture, true);\n        target.removeEventListener('drop', this.handleTopDrop);\n        target.removeEventListener('drop', this.handleTopDropCapture, true);\n    }\n    getCurrentSourceNodeOptions() {\n        const sourceId = this.monitor.getSourceId();\n        const sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n        return _objectSpread({\n            dropEffect: this.altKeyPressed ? 'copy' : 'move'\n        }, sourceNodeOptions || {});\n    }\n    getCurrentDropEffect() {\n        if (this.isDraggingNativeItem()) {\n            // It makes more sense to default to 'copy' for native resources\n            return 'copy';\n        }\n        return this.getCurrentSourceNodeOptions().dropEffect;\n    }\n    getCurrentSourcePreviewNodeOptions() {\n        const sourceId = this.monitor.getSourceId();\n        const sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n        return _objectSpread({\n            anchorX: 0.5,\n            anchorY: 0.5,\n            captureDraggingState: false\n        }, sourcePreviewNodeOptions || {});\n    }\n    isDraggingNativeItem() {\n        const itemType = this.monitor.getItemType();\n        return Object.keys(_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__).some((key)=>_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__[key] === itemType\n        );\n    }\n    beginDragNativeItem(type, dataTransfer) {\n        this.clearCurrentDragSourceNode();\n        this.currentNativeSource = (0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.createNativeDragSource)(type, dataTransfer);\n        this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n        this.actions.beginDrag([\n            this.currentNativeHandle\n        ]);\n    }\n    setCurrentDragSourceNode(node) {\n        this.clearCurrentDragSourceNode();\n        this.currentDragSourceNode = node;\n        // A timeout of > 0 is necessary to resolve Firefox issue referenced\n        // See:\n        //   * https://github.com/react-dnd/react-dnd/pull/928\n        //   * https://github.com/react-dnd/react-dnd/issues/869\n        const MOUSE_MOVE_TIMEOUT = 1000;\n        // Receiving a mouse event in the middle of a dragging operation\n        // means it has ended and the drag source node disappeared from DOM,\n        // so the browser didn't dispatch the dragend event.\n        //\n        // We need to wait before we start listening for mousemove events.\n        // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n        // immediately in some browsers.\n        //\n        // See:\n        //   * https://github.com/react-dnd/react-dnd/pull/928\n        //   * https://github.com/react-dnd/react-dnd/issues/869\n        //\n        this.mouseMoveTimeoutTimer = setTimeout(()=>{\n            var ref;\n            return (ref = this.rootElement) === null || ref === void 0 ? void 0 : ref.addEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n        }, MOUSE_MOVE_TIMEOUT);\n    }\n    clearCurrentDragSourceNode() {\n        if (this.currentDragSourceNode) {\n            this.currentDragSourceNode = null;\n            if (this.rootElement) {\n                var ref;\n                (ref = this.window) === null || ref === void 0 ? void 0 : ref.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n                this.rootElement.removeEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n            }\n            this.mouseMoveTimeoutTimer = null;\n            return true;\n        }\n        return false;\n    }\n    handleDragStart(e, sourceId) {\n        if (e.defaultPrevented) {\n            return;\n        }\n        if (!this.dragStartSourceIds) {\n            this.dragStartSourceIds = [];\n        }\n        this.dragStartSourceIds.unshift(sourceId);\n    }\n    handleDragEnter(_e, targetId) {\n        this.dragEnterTargetIds.unshift(targetId);\n    }\n    handleDragOver(_e, targetId) {\n        if (this.dragOverTargetIds === null) {\n            this.dragOverTargetIds = [];\n        }\n        this.dragOverTargetIds.unshift(targetId);\n    }\n    handleDrop(_e, targetId) {\n        this.dropTargetIds.unshift(targetId);\n    }\n    constructor(manager, globalContext, options){\n        this.sourcePreviewNodes = new Map();\n        this.sourcePreviewNodeOptions = new Map();\n        this.sourceNodes = new Map();\n        this.sourceNodeOptions = new Map();\n        this.dragStartSourceIds = null;\n        this.dropTargetIds = [];\n        this.dragEnterTargetIds = [];\n        this.currentNativeSource = null;\n        this.currentNativeHandle = null;\n        this.currentDragSourceNode = null;\n        this.altKeyPressed = false;\n        this.mouseMoveTimeoutTimer = null;\n        this.asyncEndDragFrameId = null;\n        this.dragOverTargetIds = null;\n        this.lastClientOffset = null;\n        this.hoverRafId = null;\n        this.getSourceClientOffset = (sourceId)=>{\n            const source = this.sourceNodes.get(sourceId);\n            return source && (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getNodeClientOffset)(source) || null;\n        };\n        this.endDragNativeItem = ()=>{\n            if (!this.isDraggingNativeItem()) {\n                return;\n            }\n            this.actions.endDrag();\n            if (this.currentNativeHandle) {\n                this.registry.removeSource(this.currentNativeHandle);\n            }\n            this.currentNativeHandle = null;\n            this.currentNativeSource = null;\n        };\n        this.isNodeInDocument = (node)=>{\n            // Check the node either in the main document or in the current context\n            return Boolean(node && this.document && this.document.body && this.document.body.contains(node));\n        };\n        this.endDragIfSourceWasRemovedFromDOM = ()=>{\n            const node = this.currentDragSourceNode;\n            if (node == null || this.isNodeInDocument(node)) {\n                return;\n            }\n            if (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n                this.actions.endDrag();\n            }\n            this.cancelHover();\n        };\n        this.scheduleHover = (dragOverTargetIds)=>{\n            if (this.hoverRafId === null && typeof requestAnimationFrame !== 'undefined') {\n                this.hoverRafId = requestAnimationFrame(()=>{\n                    if (this.monitor.isDragging()) {\n                        this.actions.hover(dragOverTargetIds || [], {\n                            clientOffset: this.lastClientOffset\n                        });\n                    }\n                    this.hoverRafId = null;\n                });\n            }\n        };\n        this.cancelHover = ()=>{\n            if (this.hoverRafId !== null && typeof cancelAnimationFrame !== 'undefined') {\n                cancelAnimationFrame(this.hoverRafId);\n                this.hoverRafId = null;\n            }\n        };\n        this.handleTopDragStartCapture = ()=>{\n            this.clearCurrentDragSourceNode();\n            this.dragStartSourceIds = [];\n        };\n        this.handleTopDragStart = (e)=>{\n            if (e.defaultPrevented) {\n                return;\n            }\n            const { dragStartSourceIds  } = this;\n            this.dragStartSourceIds = null;\n            const clientOffset = (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e);\n            // Avoid crashing if we missed a drop event or our previous drag died\n            if (this.monitor.isDragging()) {\n                this.actions.endDrag();\n                this.cancelHover();\n            }\n            // Don't publish the source just yet (see why below)\n            this.actions.beginDrag(dragStartSourceIds || [], {\n                publishSource: false,\n                getSourceClientOffset: this.getSourceClientOffset,\n                clientOffset\n            });\n            const { dataTransfer  } = e;\n            const nativeType = (0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(dataTransfer);\n            if (this.monitor.isDragging()) {\n                if (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n                    // Use custom drag image if user specifies it.\n                    // If child drag source refuses drag but parent agrees,\n                    // use parent's node as drag image. Neither works in IE though.\n                    const sourceId = this.monitor.getSourceId();\n                    const sourceNode = this.sourceNodes.get(sourceId);\n                    const dragPreview = this.sourcePreviewNodes.get(sourceId) || sourceNode;\n                    if (dragPreview) {\n                        const { anchorX , anchorY , offsetX , offsetY  } = this.getCurrentSourcePreviewNodeOptions();\n                        const anchorPoint = {\n                            anchorX,\n                            anchorY\n                        };\n                        const offsetPoint = {\n                            offsetX,\n                            offsetY\n                        };\n                        const dragPreviewOffset = (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getDragPreviewOffset)(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n                        dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n                    }\n                }\n                try {\n                    // Firefox won't drag without setting data\n                    dataTransfer === null || dataTransfer === void 0 ? void 0 : dataTransfer.setData('application/json', {});\n                } catch (err) {\n                // IE doesn't support MIME types in setData\n                }\n                // Store drag source node so we can check whether\n                // it is removed from DOM and trigger endDrag manually.\n                this.setCurrentDragSourceNode(e.target);\n                // Now we are ready to publish the drag source.. or are we not?\n                const { captureDraggingState  } = this.getCurrentSourcePreviewNodeOptions();\n                if (!captureDraggingState) {\n                    // Usually we want to publish it in the next tick so that browser\n                    // is able to screenshot the current (not yet dragging) state.\n                    //\n                    // It also neatly avoids a situation where render() returns null\n                    // in the same tick for the source element, and browser freaks out.\n                    setTimeout(()=>this.actions.publishDragSource()\n                    , 0);\n                } else {\n                    // In some cases the user may want to override this behavior, e.g.\n                    // to work around IE not supporting custom drag previews.\n                    //\n                    // When using a custom drag layer, the only way to prevent\n                    // the default drag preview from drawing in IE is to screenshot\n                    // the dragging state in which the node itself has zero opacity\n                    // and height. In this case, though, returning null from render()\n                    // will abruptly end the dragging, which is not obvious.\n                    //\n                    // This is the reason such behavior is strictly opt-in.\n                    this.actions.publishDragSource();\n                }\n            } else if (nativeType) {\n                // A native item (such as URL) dragged from inside the document\n                this.beginDragNativeItem(nativeType);\n            } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute('draggable'))) {\n                // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n                // Just let it drag. It's a native type (URL or text) and will be picked up in\n                // dragenter handler.\n                return;\n            } else {\n                // If by this time no drag source reacted, tell browser not to drag.\n                e.preventDefault();\n            }\n        };\n        this.handleTopDragEndCapture = ()=>{\n            if (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n                // Firefox can dispatch this event in an infinite loop\n                // if dragend handler does something like showing an alert.\n                // Only proceed if we have not handled it already.\n                this.actions.endDrag();\n            }\n            this.cancelHover();\n        };\n        this.handleTopDragEnterCapture = (e)=>{\n            this.dragEnterTargetIds = [];\n            if (this.isDraggingNativeItem()) {\n                var ref;\n                (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n            }\n            const isFirstEnter = this.enterLeaveCounter.enter(e.target);\n            if (!isFirstEnter || this.monitor.isDragging()) {\n                return;\n            }\n            const { dataTransfer  } = e;\n            const nativeType = (0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(dataTransfer);\n            if (nativeType) {\n                // A native item (such as file or URL) dragged from outside the document\n                this.beginDragNativeItem(nativeType, dataTransfer);\n            }\n        };\n        this.handleTopDragEnter = (e)=>{\n            const { dragEnterTargetIds  } = this;\n            this.dragEnterTargetIds = [];\n            if (!this.monitor.isDragging()) {\n                // This is probably a native item type we don't understand.\n                return;\n            }\n            this.altKeyPressed = e.altKey;\n            // If the target changes position as the result of `dragenter`, `dragover` might still\n            // get dispatched despite target being no longer there. The easy solution is to check\n            // whether there actually is a target before firing `hover`.\n            if (dragEnterTargetIds.length > 0) {\n                this.actions.hover(dragEnterTargetIds, {\n                    clientOffset: (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e)\n                });\n            }\n            const canDrop = dragEnterTargetIds.some((targetId)=>this.monitor.canDropOnTarget(targetId)\n            );\n            if (canDrop) {\n                // IE requires this to fire dragover events\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = this.getCurrentDropEffect();\n                }\n            }\n        };\n        this.handleTopDragOverCapture = (e)=>{\n            this.dragOverTargetIds = [];\n            if (this.isDraggingNativeItem()) {\n                var ref;\n                (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n            }\n        };\n        this.handleTopDragOver = (e)=>{\n            const { dragOverTargetIds  } = this;\n            this.dragOverTargetIds = [];\n            if (!this.monitor.isDragging()) {\n                // This is probably a native item type we don't understand.\n                // Prevent default \"drop and blow away the whole document\" action.\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = 'none';\n                }\n                return;\n            }\n            this.altKeyPressed = e.altKey;\n            this.lastClientOffset = (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e);\n            this.scheduleHover(dragOverTargetIds);\n            const canDrop = (dragOverTargetIds || []).some((targetId)=>this.monitor.canDropOnTarget(targetId)\n            );\n            if (canDrop) {\n                // Show user-specified drop effect.\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = this.getCurrentDropEffect();\n                }\n            } else if (this.isDraggingNativeItem()) {\n                // Don't show a nice cursor but still prevent default\n                // \"drop and blow away the whole document\" action.\n                e.preventDefault();\n            } else {\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = 'none';\n                }\n            }\n        };\n        this.handleTopDragLeaveCapture = (e)=>{\n            if (this.isDraggingNativeItem()) {\n                e.preventDefault();\n            }\n            const isLastLeave = this.enterLeaveCounter.leave(e.target);\n            if (!isLastLeave) {\n                return;\n            }\n            if (this.isDraggingNativeItem()) {\n                setTimeout(()=>this.endDragNativeItem()\n                , 0);\n            }\n            this.cancelHover();\n        };\n        this.handleTopDropCapture = (e)=>{\n            this.dropTargetIds = [];\n            if (this.isDraggingNativeItem()) {\n                var ref;\n                e.preventDefault();\n                (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n            } else if ((0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(e.dataTransfer)) {\n                // Dragging some elements, like <a> and <img> may still behave like a native drag event,\n                // even if the current drag event matches a user-defined type.\n                // Stop the default behavior when we're not expecting a native item to be dropped.\n                e.preventDefault();\n            }\n            this.enterLeaveCounter.reset();\n        };\n        this.handleTopDrop = (e)=>{\n            const { dropTargetIds  } = this;\n            this.dropTargetIds = [];\n            this.actions.hover(dropTargetIds, {\n                clientOffset: (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e)\n            });\n            this.actions.drop({\n                dropEffect: this.getCurrentDropEffect()\n            });\n            if (this.isDraggingNativeItem()) {\n                this.endDragNativeItem();\n            } else if (this.monitor.isDragging()) {\n                this.actions.endDrag();\n            }\n            this.cancelHover();\n        };\n        this.handleSelectStart = (e)=>{\n            const target = e.target;\n            // Only IE requires us to explicitly say\n            // we want drag drop operation to start\n            if (typeof target.dragDrop !== 'function') {\n                return;\n            }\n            // Inputs and textareas should be selectable\n            if (target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n                return;\n            }\n            // For other targets, ask IE\n            // to enable drag and drop\n            e.preventDefault();\n            target.dragDrop();\n        };\n        this.options = new _OptionsReader_js__WEBPACK_IMPORTED_MODULE_3__.OptionsReader(globalContext, options);\n        this.actions = manager.getActions();\n        this.monitor = manager.getMonitor();\n        this.registry = manager.getRegistry();\n        this.enterLeaveCounter = new _EnterLeaveCounter_js__WEBPACK_IMPORTED_MODULE_4__.EnterLeaveCounter(this.isNodeInDocument);\n    }\n}\n\n//# sourceMappingURL=HTML5BackendImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonotonicInterpolant: () => (/* binding */ MonotonicInterpolant)\n/* harmony export */ });\nclass MonotonicInterpolant {\n    interpolate(x) {\n        const { xs , ys , c1s , c2s , c3s  } = this;\n        // The rightmost point in the dataset should give an exact result\n        let i = xs.length - 1;\n        if (x === xs[i]) {\n            return ys[i];\n        }\n        // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n        let low = 0;\n        let high = c3s.length - 1;\n        let mid;\n        while(low <= high){\n            mid = Math.floor(0.5 * (low + high));\n            const xHere = xs[mid];\n            if (xHere < x) {\n                low = mid + 1;\n            } else if (xHere > x) {\n                high = mid - 1;\n            } else {\n                return ys[mid];\n            }\n        }\n        i = Math.max(0, high);\n        // Interpolate\n        const diff = x - xs[i];\n        const diffSq = diff * diff;\n        return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n    }\n    constructor(xs, ys){\n        const { length  } = xs;\n        // Rearrange xs and ys so that xs is sorted\n        const indexes = [];\n        for(let i = 0; i < length; i++){\n            indexes.push(i);\n        }\n        indexes.sort((a, b)=>xs[a] < xs[b] ? -1 : 1\n        );\n        // Get consecutive differences and slopes\n        const dys = [];\n        const dxs = [];\n        const ms = [];\n        let dx;\n        let dy;\n        for(let i1 = 0; i1 < length - 1; i1++){\n            dx = xs[i1 + 1] - xs[i1];\n            dy = ys[i1 + 1] - ys[i1];\n            dxs.push(dx);\n            dys.push(dy);\n            ms.push(dy / dx);\n        }\n        // Get degree-1 coefficients\n        const c1s = [\n            ms[0]\n        ];\n        for(let i2 = 0; i2 < dxs.length - 1; i2++){\n            const m2 = ms[i2];\n            const mNext = ms[i2 + 1];\n            if (m2 * mNext <= 0) {\n                c1s.push(0);\n            } else {\n                dx = dxs[i2];\n                const dxNext = dxs[i2 + 1];\n                const common = dx + dxNext;\n                c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n            }\n        }\n        c1s.push(ms[ms.length - 1]);\n        // Get degree-2 and degree-3 coefficients\n        const c2s = [];\n        const c3s = [];\n        let m;\n        for(let i3 = 0; i3 < c1s.length - 1; i3++){\n            m = ms[i3];\n            const c1 = c1s[i3];\n            const invDx = 1 / dxs[i3];\n            const common = c1 + c1s[i3 + 1] - m - m;\n            c2s.push((m - c1 - common) * invDx);\n            c3s.push(common * invDx * invDx);\n        }\n        this.xs = xs;\n        this.ys = ys;\n        this.c1s = c1s;\n        this.c2s = c2s;\n        this.c3s = c3s;\n    }\n}\n\n//# sourceMappingURL=MonotonicInterpolant.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeDragSource: () => (/* binding */ NativeDragSource)\n/* harmony export */ });\nclass NativeDragSource {\n    initializeExposedProperties() {\n        Object.keys(this.config.exposeProperties).forEach((property)=>{\n            Object.defineProperty(this.item, property, {\n                configurable: true,\n                enumerable: true,\n                get () {\n                    // eslint-disable-next-line no-console\n                    console.warn(`Browser doesn't allow reading \"${property}\" until the drop event.`);\n                    return null;\n                }\n            });\n        });\n    }\n    loadDataTransfer(dataTransfer) {\n        if (dataTransfer) {\n            const newProperties = {};\n            Object.keys(this.config.exposeProperties).forEach((property)=>{\n                const propertyFn = this.config.exposeProperties[property];\n                if (propertyFn != null) {\n                    newProperties[property] = {\n                        value: propertyFn(dataTransfer, this.config.matchesTypes),\n                        configurable: true,\n                        enumerable: true\n                    };\n                }\n            });\n            Object.defineProperties(this.item, newProperties);\n        }\n    }\n    canDrag() {\n        return true;\n    }\n    beginDrag() {\n        return this.item;\n    }\n    isDragging(monitor, handle) {\n        return handle === monitor.getSourceId();\n    }\n    endDrag() {\n    // empty\n    }\n    constructor(config){\n        this.config = config;\n        this.item = {};\n        this.initializeExposedProperties();\n    }\n}\n\n//# sourceMappingURL=NativeDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVEcmFnU291cmNlcy9OYXRpdmVEcmFnU291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUVBQW1FLFNBQVM7QUFDNUU7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVEcmFnU291cmNlcy9OYXRpdmVEcmFnU291cmNlLmpzP2NlZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIE5hdGl2ZURyYWdTb3VyY2Uge1xuICAgIGluaXRpYWxpemVFeHBvc2VkUHJvcGVydGllcygpIHtcbiAgICAgICAgT2JqZWN0LmtleXModGhpcy5jb25maWcuZXhwb3NlUHJvcGVydGllcykuZm9yRWFjaCgocHJvcGVydHkpPT57XG4gICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcy5pdGVtLCBwcm9wZXJ0eSwge1xuICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgICAgIGdldCAoKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zb2xlXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgQnJvd3NlciBkb2Vzbid0IGFsbG93IHJlYWRpbmcgXCIke3Byb3BlcnR5fVwiIHVudGlsIHRoZSBkcm9wIGV2ZW50LmApO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGxvYWREYXRhVHJhbnNmZXIoZGF0YVRyYW5zZmVyKSB7XG4gICAgICAgIGlmIChkYXRhVHJhbnNmZXIpIHtcbiAgICAgICAgICAgIGNvbnN0IG5ld1Byb3BlcnRpZXMgPSB7fTtcbiAgICAgICAgICAgIE9iamVjdC5rZXlzKHRoaXMuY29uZmlnLmV4cG9zZVByb3BlcnRpZXMpLmZvckVhY2goKHByb3BlcnR5KT0+e1xuICAgICAgICAgICAgICAgIGNvbnN0IHByb3BlcnR5Rm4gPSB0aGlzLmNvbmZpZy5leHBvc2VQcm9wZXJ0aWVzW3Byb3BlcnR5XTtcbiAgICAgICAgICAgICAgICBpZiAocHJvcGVydHlGbiAhPSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIG5ld1Byb3BlcnRpZXNbcHJvcGVydHldID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHByb3BlcnR5Rm4oZGF0YVRyYW5zZmVyLCB0aGlzLmNvbmZpZy5tYXRjaGVzVHlwZXMpLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGhpcy5pdGVtLCBuZXdQcm9wZXJ0aWVzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjYW5EcmFnKCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgYmVnaW5EcmFnKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5pdGVtO1xuICAgIH1cbiAgICBpc0RyYWdnaW5nKG1vbml0b3IsIGhhbmRsZSkge1xuICAgICAgICByZXR1cm4gaGFuZGxlID09PSBtb25pdG9yLmdldFNvdXJjZUlkKCk7XG4gICAgfVxuICAgIGVuZERyYWcoKSB7XG4gICAgLy8gZW1wdHlcbiAgICB9XG4gICAgY29uc3RydWN0b3IoY29uZmlnKXtcbiAgICAgICAgdGhpcy5jb25maWcgPSBjb25maWc7XG4gICAgICAgIHRoaXMuaXRlbSA9IHt9O1xuICAgICAgICB0aGlzLmluaXRpYWxpemVFeHBvc2VkUHJvcGVydGllcygpO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9TmF0aXZlRHJhZ1NvdXJjZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataFromDataTransfer: () => (/* binding */ getDataFromDataTransfer)\n/* harmony export */ });\nfunction getDataFromDataTransfer(dataTransfer, typesToTry, defaultValue) {\n    const result = typesToTry.reduce((resultSoFar, typeToTry)=>resultSoFar || dataTransfer.getData(typeToTry)\n    , '');\n    return result != null ? result : defaultValue;\n}\n\n//# sourceMappingURL=getDataFromDataTransfer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVEcmFnU291cmNlcy9nZXREYXRhRnJvbURhdGFUcmFuc2Zlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvTmF0aXZlRHJhZ1NvdXJjZXMvZ2V0RGF0YUZyb21EYXRhVHJhbnNmZXIuanM/ZGQzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZ2V0RGF0YUZyb21EYXRhVHJhbnNmZXIoZGF0YVRyYW5zZmVyLCB0eXBlc1RvVHJ5LCBkZWZhdWx0VmFsdWUpIHtcbiAgICBjb25zdCByZXN1bHQgPSB0eXBlc1RvVHJ5LnJlZHVjZSgocmVzdWx0U29GYXIsIHR5cGVUb1RyeSk9PnJlc3VsdFNvRmFyIHx8IGRhdGFUcmFuc2Zlci5nZXREYXRhKHR5cGVUb1RyeSlcbiAgICAsICcnKTtcbiAgICByZXR1cm4gcmVzdWx0ICE9IG51bGwgPyByZXN1bHQgOiBkZWZhdWx0VmFsdWU7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldERhdGFGcm9tRGF0YVRyYW5zZmVyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNativeDragSource: () => (/* binding */ createNativeDragSource),\n/* harmony export */   matchNativeItemType: () => (/* binding */ matchNativeItemType)\n/* harmony export */ });\n/* harmony import */ var _NativeDragSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeDragSource.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js\");\n/* harmony import */ var _nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./nativeTypesConfig.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js\");\n\n\nfunction createNativeDragSource(type, dataTransfer) {\n    const config = _nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__.nativeTypesConfig[type];\n    if (!config) {\n        throw new Error(`native type ${type} has no configuration`);\n    }\n    const result = new _NativeDragSource_js__WEBPACK_IMPORTED_MODULE_1__.NativeDragSource(config);\n    result.loadDataTransfer(dataTransfer);\n    return result;\n}\nfunction matchNativeItemType(dataTransfer) {\n    if (!dataTransfer) {\n        return null;\n    }\n    const dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || []);\n    return Object.keys(_nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__.nativeTypesConfig).filter((nativeItemType)=>{\n        const typeConfig = _nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__.nativeTypesConfig[nativeItemType];\n        if (!(typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.matchesTypes)) {\n            return false;\n        }\n        return typeConfig.matchesTypes.some((t)=>dataTransferTypes.indexOf(t) > -1\n        );\n    })[0] || null;\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nativeTypesConfig: () => (/* binding */ nativeTypesConfig)\n/* harmony export */ });\n/* harmony import */ var _NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../NativeTypes.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\");\n/* harmony import */ var _getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDataFromDataTransfer.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js\");\n\n\nconst nativeTypesConfig = {\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.FILE]: {\n        exposeProperties: {\n            files: (dataTransfer)=>Array.prototype.slice.call(dataTransfer.files)\n            ,\n            items: (dataTransfer)=>dataTransfer.items\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Files'\n        ]\n    },\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.HTML]: {\n        exposeProperties: {\n            html: (dataTransfer, matchesTypes)=>(0,_getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, '')\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Html',\n            'text/html'\n        ]\n    },\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.URL]: {\n        exposeProperties: {\n            urls: (dataTransfer, matchesTypes)=>(0,_getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, '').split('\\n')\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Url',\n            'text/uri-list'\n        ]\n    },\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.TEXT]: {\n        exposeProperties: {\n            text: (dataTransfer, matchesTypes)=>(0,_getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, '')\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Text',\n            'text/plain'\n        ]\n    }\n};\n\n//# sourceMappingURL=nativeTypesConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeTypes.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FILE: () => (/* binding */ FILE),\n/* harmony export */   HTML: () => (/* binding */ HTML),\n/* harmony export */   TEXT: () => (/* binding */ TEXT),\n/* harmony export */   URL: () => (/* binding */ URL)\n/* harmony export */ });\nconst FILE = '__NATIVE_FILE__';\nconst URL = '__NATIVE_URL__';\nconst TEXT = '__NATIVE_TEXT__';\nconst HTML = '__NATIVE_HTML__';\n\n//# sourceMappingURL=NativeTypes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVUeXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L05hdGl2ZVR5cGVzLmpzPzk5YzkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEZJTEUgPSAnX19OQVRJVkVfRklMRV9fJztcbmV4cG9ydCBjb25zdCBVUkwgPSAnX19OQVRJVkVfVVJMX18nO1xuZXhwb3J0IGNvbnN0IFRFWFQgPSAnX19OQVRJVkVfVEVYVF9fJztcbmV4cG9ydCBjb25zdCBIVE1MID0gJ19fTkFUSVZFX0hUTUxfXyc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU5hdGl2ZVR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDragPreviewOffset: () => (/* binding */ getDragPreviewOffset),\n/* harmony export */   getEventClientOffset: () => (/* binding */ getEventClientOffset),\n/* harmony export */   getNodeClientOffset: () => (/* binding */ getNodeClientOffset)\n/* harmony export */ });\n/* harmony import */ var _BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BrowserDetector.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js\");\n/* harmony import */ var _MonotonicInterpolant_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MonotonicInterpolant.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js\");\n\n\nconst ELEMENT_NODE = 1;\nfunction getNodeClientOffset(node) {\n    const el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n    if (!el) {\n        return null;\n    }\n    const { top , left  } = el.getBoundingClientRect();\n    return {\n        x: left,\n        y: top\n    };\n}\nfunction getEventClientOffset(e) {\n    return {\n        x: e.clientX,\n        y: e.clientY\n    };\n}\nfunction isImageNode(node) {\n    var ref;\n    return node.nodeName === 'IMG' && ((0,_BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__.isFirefox)() || !((ref = document.documentElement) === null || ref === void 0 ? void 0 : ref.contains(node)));\n}\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n    let dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n    let dragPreviewHeight = isImage ? dragPreview.height : sourceHeight;\n    // Work around @2x coordinate discrepancies in browsers\n    if ((0,_BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__.isSafari)() && isImage) {\n        dragPreviewHeight /= window.devicePixelRatio;\n        dragPreviewWidth /= window.devicePixelRatio;\n    }\n    return {\n        dragPreviewWidth,\n        dragPreviewHeight\n    };\n}\nfunction getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n    // The browsers will use the image intrinsic size under different conditions.\n    // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n    const isImage = isImageNode(dragPreview);\n    const dragPreviewNode = isImage ? sourceNode : dragPreview;\n    const dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n    const offsetFromDragPreview = {\n        x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n        y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n    };\n    const { offsetWidth: sourceWidth , offsetHeight: sourceHeight  } = sourceNode;\n    const { anchorX , anchorY  } = anchorPoint;\n    const { dragPreviewWidth , dragPreviewHeight  } = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight);\n    const calculateYOffset = ()=>{\n        const interpolantY = new _MonotonicInterpolant_js__WEBPACK_IMPORTED_MODULE_1__.MonotonicInterpolant([\n            0,\n            0.5,\n            1\n        ], [\n            // Dock to the top\n            offsetFromDragPreview.y,\n            // Align at the center\n            (offsetFromDragPreview.y / sourceHeight) * dragPreviewHeight,\n            // Dock to the bottom\n            offsetFromDragPreview.y + dragPreviewHeight - sourceHeight, \n        ]);\n        let y = interpolantY.interpolate(anchorY);\n        // Work around Safari 8 positioning bug\n        if ((0,_BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__.isSafari)() && isImage) {\n            // We'll have to wait for @3x to see if this is entirely correct\n            y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n        }\n        return y;\n    };\n    const calculateXOffset = ()=>{\n        // Interpolate coordinates depending on anchor point\n        // If you know a simpler way to do this, let me know\n        const interpolantX = new _MonotonicInterpolant_js__WEBPACK_IMPORTED_MODULE_1__.MonotonicInterpolant([\n            0,\n            0.5,\n            1\n        ], [\n            // Dock to the left\n            offsetFromDragPreview.x,\n            // Align at the center\n            (offsetFromDragPreview.x / sourceWidth) * dragPreviewWidth,\n            // Dock to the right\n            offsetFromDragPreview.x + dragPreviewWidth - sourceWidth, \n        ]);\n        return interpolantX.interpolate(anchorX);\n    };\n    // Force offsets if specified in the options.\n    const { offsetX , offsetY  } = offsetPoint;\n    const isManualOffsetX = offsetX === 0 || offsetX;\n    const isManualOffsetY = offsetY === 0 || offsetY;\n    return {\n        x: isManualOffsetX ? offsetX : calculateXOffset(),\n        y: isManualOffsetY ? offsetY : calculateYOffset()\n    };\n}\n\n//# sourceMappingURL=OffsetUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/OptionsReader.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/OptionsReader.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionsReader: () => (/* binding */ OptionsReader)\n/* harmony export */ });\nclass OptionsReader {\n    get window() {\n        if (this.globalContext) {\n            return this.globalContext;\n        } else if (typeof window !== 'undefined') {\n            return window;\n        }\n        return undefined;\n    }\n    get document() {\n        var ref;\n        if ((ref = this.globalContext) === null || ref === void 0 ? void 0 : ref.document) {\n            return this.globalContext.document;\n        } else if (this.window) {\n            return this.window.document;\n        } else {\n            return undefined;\n        }\n    }\n    get rootElement() {\n        var ref;\n        return ((ref = this.optionsArgs) === null || ref === void 0 ? void 0 : ref.rootElement) || this.window;\n    }\n    constructor(globalContext, options){\n        this.ownerDocument = null;\n        this.globalContext = globalContext;\n        this.optionsArgs = options;\n    }\n}\n\n//# sourceMappingURL=OptionsReader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9PcHRpb25zUmVhZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9PcHRpb25zUmVhZGVyLmpzP2VjZWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIE9wdGlvbnNSZWFkZXIge1xuICAgIGdldCB3aW5kb3coKSB7XG4gICAgICAgIGlmICh0aGlzLmdsb2JhbENvbnRleHQpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmdsb2JhbENvbnRleHQ7XG4gICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHJldHVybiB3aW5kb3c7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgZ2V0IGRvY3VtZW50KCkge1xuICAgICAgICB2YXIgcmVmO1xuICAgICAgICBpZiAoKHJlZiA9IHRoaXMuZ2xvYmFsQ29udGV4dCkgPT09IG51bGwgfHwgcmVmID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZWYuZG9jdW1lbnQpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmdsb2JhbENvbnRleHQuZG9jdW1lbnQ7XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy53aW5kb3cpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLndpbmRvdy5kb2N1bWVudDtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0IHJvb3RFbGVtZW50KCkge1xuICAgICAgICB2YXIgcmVmO1xuICAgICAgICByZXR1cm4gKChyZWYgPSB0aGlzLm9wdGlvbnNBcmdzKSA9PT0gbnVsbCB8fCByZWYgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJlZi5yb290RWxlbWVudCkgfHwgdGhpcy53aW5kb3c7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKGdsb2JhbENvbnRleHQsIG9wdGlvbnMpe1xuICAgICAgICB0aGlzLm93bmVyRG9jdW1lbnQgPSBudWxsO1xuICAgICAgICB0aGlzLmdsb2JhbENvbnRleHQgPSBnbG9iYWxDb250ZXh0O1xuICAgICAgICB0aGlzLm9wdGlvbnNBcmdzID0gb3B0aW9ucztcbiAgICB9XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU9wdGlvbnNSZWFkZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/OptionsReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEmptyImage: () => (/* binding */ getEmptyImage)\n/* harmony export */ });\nlet emptyImage;\nfunction getEmptyImage() {\n    if (!emptyImage) {\n        emptyImage = new Image();\n        emptyImage.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';\n    }\n    return emptyImage;\n}\n\n//# sourceMappingURL=getEmptyImage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9nZXRFbXB0eUltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L2dldEVtcHR5SW1hZ2UuanM/OWFiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZW1wdHlJbWFnZTtcbmV4cG9ydCBmdW5jdGlvbiBnZXRFbXB0eUltYWdlKCkge1xuICAgIGlmICghZW1wdHlJbWFnZSkge1xuICAgICAgICBlbXB0eUltYWdlID0gbmV3IEltYWdlKCk7XG4gICAgICAgIGVtcHR5SW1hZ2Uuc3JjID0gJ2RhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCxSMGxHT0RsaEFRQUJBQUFBQUNINUJBRUtBQUVBTEFBQUFBQUJBQUVBQUFJQ1RBRUFPdz09JztcbiAgICB9XG4gICAgcmV0dXJuIGVtcHR5SW1hZ2U7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldEVtcHR5SW1hZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTML5Backend: () => (/* binding */ HTML5Backend),\n/* harmony export */   NativeTypes: () => (/* reexport module object */ _NativeTypes_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   getEmptyImage: () => (/* reexport safe */ _getEmptyImage_js__WEBPACK_IMPORTED_MODULE_0__.getEmptyImage)\n/* harmony export */ });\n/* harmony import */ var _HTML5BackendImpl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HTML5BackendImpl.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js\");\n/* harmony import */ var _NativeTypes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeTypes.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\");\n/* harmony import */ var _getEmptyImage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEmptyImage.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js\");\n\n\n\n\nconst HTML5Backend = function createBackend(manager, context, options) {\n    return new _HTML5BackendImpl_js__WEBPACK_IMPORTED_MODULE_2__.HTML5BackendImpl(manager, context, options);\n};\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUQ7QUFDUjtBQUNFO0FBQ1o7QUFDaEM7QUFDUCxlQUFlLGtFQUFnQjtBQUMvQjs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvaW5kZXguanM/NzI2OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIVE1MNUJhY2tlbmRJbXBsIH0gZnJvbSAnLi9IVE1MNUJhY2tlbmRJbXBsLmpzJztcbmltcG9ydCAqIGFzIF9OYXRpdmVUeXBlcyBmcm9tICcuL05hdGl2ZVR5cGVzLmpzJztcbmV4cG9ydCB7IGdldEVtcHR5SW1hZ2UgfSBmcm9tICcuL2dldEVtcHR5SW1hZ2UuanMnO1xuZXhwb3J0IHsgX05hdGl2ZVR5cGVzIGFzIE5hdGl2ZVR5cGVzIH07XG5leHBvcnQgY29uc3QgSFRNTDVCYWNrZW5kID0gZnVuY3Rpb24gY3JlYXRlQmFja2VuZChtYW5hZ2VyLCBjb250ZXh0LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIG5ldyBIVE1MNUJhY2tlbmRJbXBsKG1hbmFnZXIsIGNvbnRleHQsIG9wdGlvbnMpO1xufTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   without: () => (/* binding */ without)\n/* harmony export */ });\n// cheap lodash replacements\nfunction memoize(fn) {\n    let result = null;\n    const memoized = ()=>{\n        if (result == null) {\n            result = fn();\n        }\n        return result;\n    };\n    return memoized;\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter((i)=>i !== item\n    );\n}\nfunction union(itemsA, itemsB) {\n    const set = new Set();\n    const insertItem = (item)=>set.add(item)\n    ;\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    const result = [];\n    set.forEach((key)=>result.push(key)\n    );\n    return result;\n}\n\n//# sourceMappingURL=js_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC91dGlscy9qc191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC91dGlscy9qc191dGlscy5qcz8yMjE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGNoZWFwIGxvZGFzaCByZXBsYWNlbWVudHNcbmV4cG9ydCBmdW5jdGlvbiBtZW1vaXplKGZuKSB7XG4gICAgbGV0IHJlc3VsdCA9IG51bGw7XG4gICAgY29uc3QgbWVtb2l6ZWQgPSAoKT0+e1xuICAgICAgICBpZiAocmVzdWx0ID09IG51bGwpIHtcbiAgICAgICAgICAgIHJlc3VsdCA9IGZuKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9O1xuICAgIHJldHVybiBtZW1vaXplZDtcbn1cbi8qKlxuICogZHJvcC1pbiByZXBsYWNlbWVudCBmb3IgXy53aXRob3V0XG4gKi8gZXhwb3J0IGZ1bmN0aW9uIHdpdGhvdXQoaXRlbXMsIGl0ZW0pIHtcbiAgICByZXR1cm4gaXRlbXMuZmlsdGVyKChpKT0+aSAhPT0gaXRlbVxuICAgICk7XG59XG5leHBvcnQgZnVuY3Rpb24gdW5pb24oaXRlbXNBLCBpdGVtc0IpIHtcbiAgICBjb25zdCBzZXQgPSBuZXcgU2V0KCk7XG4gICAgY29uc3QgaW5zZXJ0SXRlbSA9IChpdGVtKT0+c2V0LmFkZChpdGVtKVxuICAgIDtcbiAgICBpdGVtc0EuZm9yRWFjaChpbnNlcnRJdGVtKTtcbiAgICBpdGVtc0IuZm9yRWFjaChpbnNlcnRJdGVtKTtcbiAgICBjb25zdCByZXN1bHQgPSBbXTtcbiAgICBzZXQuZm9yRWFjaCgoa2V5KT0+cmVzdWx0LnB1c2goa2V5KVxuICAgICk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9anNfdXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js\n");

/***/ })

};
;