# VisualVibe - Instagram Feed Curator

A modern, AI-powered Instagram feed planning and curation web application that allows users to visually arrange, preview, and optimize their Instagram feed layout before posting.

## 🚀 Features

### Core Features
- **Visual Feed Grid Preview**: 3x3 or 6x6 Instagram-style grid layout with drag-and-drop functionality
- **Content Upload & Management**: Upload images with support for JPEG, PNG, HEIC formats
- **AI-Powered Feed Optimization**: Color harmony analysis, engagement prediction, and aesthetic scoring
- **Smart Scheduling & Planning**: Calendar view with optimal posting time suggestions
- **Analytics & Insights**: Feed performance metrics and color palette analysis
- **Templates & Inspiration**: Pre-made aesthetic templates for different industries

### AI Features
- Color harmony analyzer
- Engagement prediction
- Aesthetic scoring (1-10)
- Content gap detection
- Trend integration suggestions

## 🛠 Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Drag & Drop**: React DnD
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **State Management**: React Query, Context API
- **Icons**: Lucide React
- **Image Processing**: Canvas API
- **Payment**: Stripe (ready for integration)

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### 1. Clone the repository
```bash
git clone <repository-url>
cd visualvibe-app
```

### 2. Install dependencies
```bash
npm install
# or
yarn install
```

### 3. Set up environment variables
Copy `.env.local.example` to `.env.local` and fill in your values:

```bash
cp .env.local.example .env.local
```

Required environment variables:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Instagram API Configuration (optional for MVP)
INSTAGRAM_CLIENT_ID=your_instagram_app_id
INSTAGRAM_CLIENT_SECRET=your_instagram_app_secret

# Stripe Configuration (optional for MVP)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
```

### 4. Set up Supabase

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Run the database schema:
   - Go to your Supabase dashboard
   - Navigate to SQL Editor
   - Copy and paste the contents of `database/schema.sql`
   - Execute the SQL

3. Set up Storage:
   - Go to Storage in your Supabase dashboard
   - The `images` bucket should be created automatically by the schema
   - Verify the bucket policies are in place

### 5. Run the development server
```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ai/               # AI-related components
│   ├── auth/             # Authentication components
│   ├── feed/             # Feed grid and post components
│   ├── landing/          # Landing page components
│   ├── layout/           # Layout components
│   └── upload/           # Upload components
├── contexts/             # React contexts
├── hooks/                # Custom hooks
├── lib/                  # Utility libraries
│   ├── supabase.ts      # Supabase client and helpers
│   └── utils.ts         # Utility functions
database/
└── schema.sql           # Database schema
```

## 🎨 Key Components

### FeedGrid
The main 3x3 or 6x6 grid where users arrange their Instagram posts with drag-and-drop functionality.

### ImageUploader
Handles file uploads with drag-and-drop support, image compression, and preview generation.

### AIInsights
Provides AI-powered analysis including:
- Color harmony scoring
- Engagement prediction
- Aesthetic analysis
- Optimization suggestions

### PostEditor
Allows users to edit post details including:
- Captions
- Hashtags
- Image filters
- Basic adjustments

## 🔐 Authentication

The app uses Supabase Auth with support for:
- Email/password authentication
- Social login (Google, Instagram) - ready for integration
- Row Level Security (RLS) for data protection

## 💾 Database Schema

The database includes tables for:
- `users` - User profiles and subscription info
- `projects` - Saved feed layouts
- `posts` - Individual post data
- `analytics` - Performance metrics
- `templates` - Pre-made feed templates
- `subscriptions` - Stripe subscription data

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 💰 Monetization

The app includes a freemium model with three tiers:

### Free Tier
- 3 saved feed layouts
- Basic drag-and-drop
- Upload 20 images/month
- Basic templates

### Premium Tier ($9/month)
- Unlimited saved layouts
- AI optimization features
- Advanced analytics
- Auto-posting
- Premium templates

### Pro Tier ($29/month)
- Multiple Instagram accounts
- Team collaboration
- Advanced scheduling
- Custom branding
- API access

## 🔧 Configuration

### Instagram API Setup (Optional)
1. Create a Facebook Developer account
2. Create an Instagram Basic Display app
3. Add your app credentials to environment variables
4. Configure redirect URIs

### Stripe Setup (Optional)
1. Create a Stripe account
2. Get your API keys
3. Set up webhook endpoints
4. Configure subscription products

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 📈 Performance Optimization

The app includes several performance optimizations:
- Image compression and optimization
- Lazy loading of components
- React Query for efficient data fetching
- Optimized bundle size with Next.js

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please contact [<EMAIL>](mailto:<EMAIL>) or create an issue in the repository.

## 🗺 Roadmap

### Phase 1 (Current)
- ✅ Core MVP features
- ✅ Basic AI insights
- ✅ User authentication
- ✅ Image upload and management

### Phase 2 (Next)
- [ ] Instagram API integration
- [ ] Advanced scheduling
- [ ] Payment integration
- [ ] Mobile app

### Phase 3 (Future)
- [ ] Team collaboration
- [ ] Advanced AI features
- [ ] Multi-platform support
- [ ] API for third-party integrations

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Powered by [Supabase](https://supabase.com/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons by [Lucide](https://lucide.dev/)

---

Made with ❤️ for Instagram creators worldwide
