"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-selector";
exports.ids = ["vendor-chunks/file-selector"];
exports.modules = {

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js":
/*!*****************************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file-selector.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* binding */ fromEvent)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _file__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file */ \"(ssr)/./node_modules/file-selector/dist/es2015/file.js\");\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nfunction fromEvent(evt) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file.js":
/*!********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMON_MIME_TYPES: () => (/* binding */ COMMON_MIME_TYPES),\n/* harmony export */   toFileWithPath: () => (/* binding */ toFileWithPath)\n/* harmony export */ });\nconst COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nfunction toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* reexport safe */ _file_selector__WEBPACK_IMPORTED_MODULE_0__.fromEvent)\n/* harmony export */ });\n/* harmony import */ var _file_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\");\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmlsZS1zZWxlY3Rvci9kaXN0L2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL2ZpbGUtc2VsZWN0b3IvZGlzdC9lczIwMTUvaW5kZXguanM/MzA0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBmcm9tRXZlbnQgfSBmcm9tICcuL2ZpbGUtc2VsZWN0b3InO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/index.js\n");

/***/ })

};
;