import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatRelativeTime(date: string | Date) {
  const now = new Date()
  const target = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  } else {
    return formatDate(date)
  }
}

export function generateId() {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

export function extractColors(imageElement: HTMLImageElement): Promise<string[]> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      resolve([])
      return
    }

    canvas.width = imageElement.width
    canvas.height = imageElement.height
    
    ctx.drawImage(imageElement, 0, 0)
    
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data
    
    const colorMap = new Map<string, number>()
    
    // Sample every 10th pixel for performance
    for (let i = 0; i < data.length; i += 40) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      const alpha = data[i + 3]
      
      // Skip transparent pixels
      if (alpha < 128) continue
      
      // Convert to hex
      const hex = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
      
      colorMap.set(hex, (colorMap.get(hex) || 0) + 1)
    }
    
    // Get top 5 most frequent colors
    const sortedColors = Array.from(colorMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([color]) => color)
    
    resolve(sortedColors)
  })
}

export function calculateColorHarmony(colors: string[]): number {
  if (colors.length < 2) return 100
  
  // Simple harmony calculation based on color distance
  let totalHarmony = 0
  let comparisons = 0
  
  for (let i = 0; i < colors.length; i++) {
    for (let j = i + 1; j < colors.length; j++) {
      const harmony = calculateColorDistance(colors[i], colors[j])
      totalHarmony += harmony
      comparisons++
    }
  }
  
  return comparisons > 0 ? totalHarmony / comparisons : 100
}

function calculateColorDistance(color1: string, color2: string): number {
  const rgb1 = hexToRgb(color1)
  const rgb2 = hexToRgb(color2)
  
  if (!rgb1 || !rgb2) return 0
  
  const distance = Math.sqrt(
    Math.pow(rgb1.r - rgb2.r, 2) +
    Math.pow(rgb1.g - rgb2.g, 2) +
    Math.pow(rgb1.b - rgb2.b, 2)
  )
  
  // Convert distance to harmony score (0-100)
  return Math.max(0, 100 - (distance / 441) * 100)
}

function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

export function calculateAestheticScore(posts: any[]): number {
  if (posts.length === 0) return 0
  
  let totalScore = 0
  let factors = 0
  
  // Color harmony (30%)
  const allColors = posts.flatMap(post => post.color_palette || [])
  if (allColors.length > 0) {
    const harmonyScore = calculateColorHarmony(allColors)
    totalScore += harmonyScore * 0.3
    factors += 0.3
  }
  
  // Consistency (25%)
  const hasConsistentFilters = posts.every(post => 
    post.ai_analysis?.filter_style === posts[0]?.ai_analysis?.filter_style
  )
  if (hasConsistentFilters) {
    totalScore += 85 * 0.25
  } else {
    totalScore += 60 * 0.25
  }
  factors += 0.25
  
  // Composition (25%)
  const avgComposition = posts.reduce((sum, post) => 
    sum + (post.ai_analysis?.composition_score || 75), 0
  ) / posts.length
  totalScore += avgComposition * 0.25
  factors += 0.25
  
  // Engagement potential (20%)
  const avgEngagement = posts.reduce((sum, post) => 
    sum + (post.engagement_score || 70), 0
  ) / posts.length
  totalScore += avgEngagement * 0.2
  factors += 0.2
  
  return factors > 0 ? totalScore / factors : 0
}

export function predictEngagement(post: any, feedContext: any[] = []): number {
  let score = 70 // Base score
  
  // Image quality factors
  if (post.image_width && post.image_height) {
    const resolution = post.image_width * post.image_height
    if (resolution >= 1080 * 1080) score += 10
    else if (resolution >= 720 * 720) score += 5
  }
  
  // Caption factors
  if (post.caption) {
    const captionLength = post.caption.length
    if (captionLength >= 100 && captionLength <= 300) score += 8
    else if (captionLength > 0) score += 4
  }
  
  // Hashtag factors
  if (post.hashtags && post.hashtags.length > 0) {
    const hashtagCount = post.hashtags.length
    if (hashtagCount >= 5 && hashtagCount <= 15) score += 10
    else if (hashtagCount > 0) score += 5
  }
  
  // Color harmony with feed
  if (feedContext.length > 0 && post.color_palette) {
    const feedColors = feedContext.flatMap(p => p.color_palette || [])
    const harmony = calculateColorHarmony([...feedColors, ...post.color_palette])
    score += (harmony / 100) * 15
  }
  
  // Time-based factors (mock)
  const hour = new Date().getHours()
  if (hour >= 18 && hour <= 21) score += 5 // Peak engagement hours
  
  return Math.min(100, Math.max(0, score))
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic']
  return validTypes.includes(file.type)
}

export function compressImage(file: File, maxWidth: number = 1080, quality: number = 0.8): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
      canvas.width = img.width * ratio
      canvas.height = img.height * ratio
      
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)
      
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now()
          })
          resolve(compressedFile)
        } else {
          resolve(file)
        }
      }, 'image/jpeg', quality)
    }
    
    img.src = URL.createObjectURL(file)
  })
}
