'use client'

import { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'

export interface MediaItem {
  id: string
  name: string
  file: File
  preview: string
  size: number
  type: string
  uploadedAt: string
  tags?: string[]
}

interface MediaLibraryContextType {
  mediaItems: MediaItem[]
  selectedItems: string[]
  uploading: boolean
  addMediaItems: (files: File[]) => Promise<void>
  removeMediaItem: (id: string) => void
  selectItem: (id: string) => void
  deselectItem: (id: string) => void
  clearSelection: () => void
  searchItems: (query: string) => MediaItem[]
  getItemById: (id: string) => MediaItem | null
}

const MediaLibraryContext = createContext<MediaLibraryContextType | undefined>(undefined)

export function MediaLibraryProvider({ children }: { children: React.ReactNode }) {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([])
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [uploading, setUploading] = useState(false)

  // Load media items from localStorage on mount
  useEffect(() => {
    const loadMediaItems = () => {
      try {
        const saved = localStorage.getItem('visualvibe_media_library')
        if (saved) {
          const parsedItems = JSON.parse(saved)
          // Note: We can't restore File objects from localStorage, so we'll need to handle this differently
          // For now, we'll just load the metadata and recreate preview URLs
          setMediaItems(parsedItems.filter((item: any) => item.preview))
        }
      } catch (error) {
        console.error('Failed to load media library:', error)
      }
    }

    loadMediaItems()
  }, [])

  // Save media items to localStorage whenever they change
  useEffect(() => {
    try {
      // Only save the metadata, not the File objects
      const itemsToSave = mediaItems.map(item => ({
        id: item.id,
        name: item.name,
        preview: item.preview,
        size: item.size,
        type: item.type,
        uploadedAt: item.uploadedAt,
        tags: item.tags
      }))
      localStorage.setItem('visualvibe_media_library', JSON.stringify(itemsToSave))
    } catch (error) {
      console.error('Failed to save media library:', error)
    }
  }, [mediaItems])

  const addMediaItems = useCallback(async (files: File[]) => {
    setUploading(true)
    
    try {
      const newItems: MediaItem[] = []
      
      for (const file of files) {
        // Create preview using FileReader for data URL (more stable than blob URLs)
        const preview = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.readAsDataURL(file)
        })

        const mediaItem: MediaItem = {
          id: uuidv4(),
          name: file.name,
          file,
          preview,
          size: file.size,
          type: file.type,
          uploadedAt: new Date().toISOString(),
          tags: []
        }

        newItems.push(mediaItem)
      }

      setMediaItems(prev => [...newItems, ...prev]) // Add new items to the beginning
      console.log(`Added ${newItems.length} items to media library`)
      
    } catch (error) {
      console.error('Failed to add media items:', error)
    } finally {
      setUploading(false)
    }
  }, [])

  const removeMediaItem = useCallback((id: string) => {
    setMediaItems(prev => prev.filter(item => item.id !== id))
    setSelectedItems(prev => prev.filter(itemId => itemId !== id))
  }, [])

  const selectItem = useCallback((id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) ? prev : [...prev, id]
    )
  }, [])

  const deselectItem = useCallback((id: string) => {
    setSelectedItems(prev => prev.filter(itemId => itemId !== id))
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedItems([])
  }, [])

  const searchItems = useCallback((query: string) => {
    if (!query.trim()) return mediaItems
    
    const lowercaseQuery = query.toLowerCase()
    return mediaItems.filter(item => 
      item.name.toLowerCase().includes(lowercaseQuery) ||
      item.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    )
  }, [mediaItems])

  const getItemById = useCallback((id: string) => {
    return mediaItems.find(item => item.id === id) || null
  }, [mediaItems])

  const value = {
    mediaItems,
    selectedItems,
    uploading,
    addMediaItems,
    removeMediaItem,
    selectItem,
    deselectItem,
    clearSelection,
    searchItems,
    getItemById,
  }

  return (
    <MediaLibraryContext.Provider value={value}>
      {children}
    </MediaLibraryContext.Provider>
  )
}

export function useMediaLibrary() {
  const context = useContext(MediaLibraryContext)
  if (context === undefined) {
    throw new Error('useMediaLibrary must be used within a MediaLibraryProvider')
  }
  return context
}
