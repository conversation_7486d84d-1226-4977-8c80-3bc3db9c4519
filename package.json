{"name": "visualvibe-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-query": "^3.39.3", "@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "react-hook-form": "^7.48.2", "react-dropzone": "^14.2.3", "canvas": "^2.11.2", "date-fns": "^2.30.0", "uuid": "^9.0.1", "stripe": "^14.9.0", "@stripe/stripe-js": "^2.2.0"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "eslint": "^8.56.0", "eslint-config-next": "14.0.4"}}