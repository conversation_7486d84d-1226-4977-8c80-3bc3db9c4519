/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-query";
exports.ids = ["vendor-chunks/react-query"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-query/es/core/focusManager.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/core/focusManager.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\nvar FocusManager = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(FocusManager, _Subscribable);\n\n  function FocusManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onFocus) {\n      var _window;\n\n      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = FocusManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n\n    if (focused) {\n      this.onFocus();\n    }\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n\n  return FocusManager;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);\nvar focusManager = new FocusManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/hydration.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-query/es/core/hydration.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dehydrate: () => (/* binding */ dehydrate),\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\n\n// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\n\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\n\nfunction dehydrate(client, options) {\n  var _options, _options2;\n\n  options = options || {};\n  var mutations = [];\n  var queries = [];\n\n  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {\n    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(function (mutation) {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {\n    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(function (query) {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations: mutations,\n    queries: queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  var mutationCache = client.getMutationCache();\n  var queryCache = client.getQueryCache();\n  var mutations = dehydratedState.mutations || [];\n  var queries = dehydratedState.queries || [];\n  mutations.forEach(function (dehydratedMutation) {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {\n      mutationKey: dehydratedMutation.mutationKey\n    }), dehydratedMutation.state);\n  });\n  queries.forEach(function (dehydratedQuery) {\n    var _options$defaultOptio2;\n\n    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {\n      queryKey: dehydratedQuery.queryKey,\n      queryHash: dehydratedQuery.queryHash\n    }), dehydratedQuery.state);\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/hydration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/index.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.CancelledError),\n/* harmony export */   InfiniteQueryObserver: () => (/* reexport safe */ _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__.InfiniteQueryObserver),\n/* harmony export */   MutationCache: () => (/* reexport safe */ _mutationCache__WEBPACK_IMPORTED_MODULE_6__.MutationCache),\n/* harmony export */   MutationObserver: () => (/* reexport safe */ _mutationObserver__WEBPACK_IMPORTED_MODULE_7__.MutationObserver),\n/* harmony export */   QueriesObserver: () => (/* reexport safe */ _queriesObserver__WEBPACK_IMPORTED_MODULE_4__.QueriesObserver),\n/* harmony export */   QueryCache: () => (/* reexport safe */ _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache),\n/* harmony export */   QueryClient: () => (/* reexport safe */ _queryClient__WEBPACK_IMPORTED_MODULE_2__.QueryClient),\n/* harmony export */   QueryObserver: () => (/* reexport safe */ _queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver),\n/* harmony export */   dehydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.dehydrate),\n/* harmony export */   focusManager: () => (/* reexport safe */ _focusManager__WEBPACK_IMPORTED_MODULE_10__.focusManager),\n/* harmony export */   hashQueryKey: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.hashQueryKey),\n/* harmony export */   hydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.hydrate),\n/* harmony export */   isCancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.isCancelledError),\n/* harmony export */   isError: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.isError),\n/* harmony export */   notifyManager: () => (/* reexport safe */ _notifyManager__WEBPACK_IMPORTED_MODULE_9__.notifyManager),\n/* harmony export */   onlineManager: () => (/* reexport safe */ _onlineManager__WEBPACK_IMPORTED_MODULE_11__.onlineManager),\n/* harmony export */   setLogger: () => (/* reexport safe */ _logger__WEBPACK_IMPORTED_MODULE_8__.setLogger)\n/* harmony export */ });\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ \"(ssr)/./node_modules/react-query/es/core/queryCache.js\");\n/* harmony import */ var _queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./queryClient */ \"(ssr)/./node_modules/react-query/es/core/queryClient.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _queriesObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queriesObserver */ \"(ssr)/./node_modules/react-query/es/core/queriesObserver.js\");\n/* harmony import */ var _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./infiniteQueryObserver */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\");\n/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mutationCache */ \"(ssr)/./node_modules/react-query/es/core/mutationCache.js\");\n/* harmony import */ var _mutationObserver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./mutationObserver */ \"(ssr)/./node_modules/react-query/es/core/mutationObserver.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _hydration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hydration */ \"(ssr)/./node_modules/react-query/es/core/hydration.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/react-query/es/core/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_14__) if([\"default\",\"CancelledError\",\"QueryCache\",\"QueryClient\",\"QueryObserver\",\"QueriesObserver\",\"InfiniteQueryObserver\",\"MutationCache\",\"MutationObserver\",\"setLogger\",\"notifyManager\",\"focusManager\",\"onlineManager\",\"hashQueryKey\",\"isError\",\"isCancelledError\",\"dehydrate\",\"hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_14__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkM7QUFDRDtBQUNFO0FBQ0k7QUFDSTtBQUNZO0FBQ2hCO0FBQ007QUFDakI7QUFDVztBQUNGO0FBQ0U7QUFDQTtBQUNIO0FBQ0ksQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL2NvcmUvaW5kZXguanM/MzZhMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBDYW5jZWxsZWRFcnJvciB9IGZyb20gJy4vcmV0cnllcic7XG5leHBvcnQgeyBRdWVyeUNhY2hlIH0gZnJvbSAnLi9xdWVyeUNhY2hlJztcbmV4cG9ydCB7IFF1ZXJ5Q2xpZW50IH0gZnJvbSAnLi9xdWVyeUNsaWVudCc7XG5leHBvcnQgeyBRdWVyeU9ic2VydmVyIH0gZnJvbSAnLi9xdWVyeU9ic2VydmVyJztcbmV4cG9ydCB7IFF1ZXJpZXNPYnNlcnZlciB9IGZyb20gJy4vcXVlcmllc09ic2VydmVyJztcbmV4cG9ydCB7IEluZmluaXRlUXVlcnlPYnNlcnZlciB9IGZyb20gJy4vaW5maW5pdGVRdWVyeU9ic2VydmVyJztcbmV4cG9ydCB7IE11dGF0aW9uQ2FjaGUgfSBmcm9tICcuL211dGF0aW9uQ2FjaGUnO1xuZXhwb3J0IHsgTXV0YXRpb25PYnNlcnZlciB9IGZyb20gJy4vbXV0YXRpb25PYnNlcnZlcic7XG5leHBvcnQgeyBzZXRMb2dnZXIgfSBmcm9tICcuL2xvZ2dlcic7XG5leHBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSAnLi9ub3RpZnlNYW5hZ2VyJztcbmV4cG9ydCB7IGZvY3VzTWFuYWdlciB9IGZyb20gJy4vZm9jdXNNYW5hZ2VyJztcbmV4cG9ydCB7IG9ubGluZU1hbmFnZXIgfSBmcm9tICcuL29ubGluZU1hbmFnZXInO1xuZXhwb3J0IHsgaGFzaFF1ZXJ5S2V5LCBpc0Vycm9yIH0gZnJvbSAnLi91dGlscyc7XG5leHBvcnQgeyBpc0NhbmNlbGxlZEVycm9yIH0gZnJvbSAnLi9yZXRyeWVyJztcbmV4cG9ydCB7IGRlaHlkcmF0ZSwgaHlkcmF0ZSB9IGZyb20gJy4vaHlkcmF0aW9uJzsgLy8gVHlwZXNcblxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-query/es/core/infiniteQueryBehavior.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextPageParam: () => (/* binding */ getNextPageParam),\n/* harmony export */   getPreviousPageParam: () => (/* binding */ getPreviousPageParam),\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\nfunction infiniteQueryBehavior() {\n  return {\n    onFetch: function onFetch(context) {\n      context.fetchFn = function () {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        var refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        var fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        var pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        var isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        var isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        var oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        var oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getAbortController)();\n        var abortSignal = abortController == null ? void 0 : abortController.signal;\n        var newPageParams = oldPageParams;\n        var cancelled = false; // Get query function\n\n        var queryFn = context.options.queryFn || function () {\n          return Promise.reject('Missing queryFn');\n        };\n\n        var buildNewPages = function buildNewPages(pages, param, page, previous) {\n          newPageParams = previous ? [param].concat(newPageParams) : [].concat(newPageParams, [param]);\n          return previous ? [page].concat(pages) : [].concat(pages, [page]);\n        }; // Create function to fetch a page\n\n\n        var fetchPage = function fetchPage(pages, manual, param, previous) {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          var queryFnContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta\n          };\n          var queryFnResult = queryFn(queryFnContext);\n          var promise = Promise.resolve(queryFnResult).then(function (page) {\n            return buildNewPages(pages, param, page, previous);\n          });\n\n          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(queryFnResult)) {\n            var promiseAsAny = promise;\n            promiseAsAny.cancel = queryFnResult.cancel;\n          }\n\n          return promise;\n        };\n\n        var promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n            var manual = typeof pageParam !== 'undefined';\n            var param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n            promise = fetchPage(oldPages, manual, param);\n          } // Fetch previous page?\n          else if (isFetchingPreviousPage) {\n              var _manual = typeof pageParam !== 'undefined';\n\n              var _param = _manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n\n              promise = fetchPage(oldPages, _manual, _param, true);\n            } // Refetch pages\n            else {\n                (function () {\n                  newPageParams = [];\n                  var manual = typeof context.options.getNextPageParam === 'undefined';\n                  var shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n                  promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n                  var _loop = function _loop(i) {\n                    promise = promise.then(function (pages) {\n                      var shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n                      if (shouldFetchNextPage) {\n                        var _param2 = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n\n                        return fetchPage(pages, manual, _param2);\n                      }\n\n                      return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n                    });\n                  };\n\n                  for (var i = 1; i < oldPages.length; i++) {\n                    _loop(i);\n                  }\n                })();\n              }\n\n        var finalPromise = promise.then(function (pages) {\n          return {\n            pages: pages,\n            pageParams: newPageParams\n          };\n        });\n        var finalPromiseAsAny = finalPromise;\n\n        finalPromiseAsAny.cancel = function () {\n          cancelled = true;\n          abortController == null ? void 0 : abortController.abort();\n\n          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(promise)) {\n            promise.cancel();\n          }\n        };\n\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    var nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    var previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-query/es/core/infiniteQueryObserver.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./infiniteQueryBehavior */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\");\n\n\n\n\nvar InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(InfiniteQueryObserver, _QueryObserver);\n\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  function InfiniteQueryObserver(client, options) {\n    return _QueryObserver.call(this, client, options) || this;\n  }\n\n  var _proto = InfiniteQueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    _QueryObserver.prototype.bindMethods.call(this);\n\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    _QueryObserver.prototype.setOptions.call(this, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n      behavior: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)()\n    }), notifyOptions);\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    options.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)();\n    return _QueryObserver.prototype.getOptimisticResult.call(this, options);\n  };\n\n  _proto.fetchNextPage = function fetchNextPage(options) {\n    var _options$cancelRefetc;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.fetchPreviousPage = function fetchPreviousPage(options) {\n    var _options$cancelRefetc2;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;\n\n    var state = query.state;\n\n    var result = _QueryObserver.prototype.createResult.call(this, query, options);\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, result, {\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasNextPage)(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasPreviousPage)(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',\n      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'\n    });\n  };\n\n  return InfiniteQueryObserver;\n}(_queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/logger.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/core/logger.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLogger: () => (/* binding */ getLogger),\n/* harmony export */   setLogger: () => (/* binding */ setLogger)\n/* harmony export */ });\n// TYPES\n// FUNCTIONS\nvar logger = console;\nfunction getLogger() {\n  return logger;\n}\nfunction setLogger(newLogger) {\n  logger = newLogger;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9sb2dnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9jb3JlL2xvZ2dlci5qcz84NmZjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRZUEVTXG4vLyBGVU5DVElPTlNcbnZhciBsb2dnZXIgPSBjb25zb2xlO1xuZXhwb3J0IGZ1bmN0aW9uIGdldExvZ2dlcigpIHtcbiAgcmV0dXJuIGxvZ2dlcjtcbn1cbmV4cG9ydCBmdW5jdGlvbiBzZXRMb2dnZXIobmV3TG9nZ2VyKSB7XG4gIGxvZ2dlciA9IG5ld0xvZ2dlcjtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutation.js":
/*!******************************************************!*\
  !*** ./node_modules/react-query/es/core/mutation.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar Mutation = /*#__PURE__*/function () {\n  function Mutation(config) {\n    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config.defaultOptions, config.options);\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.meta = config.meta;\n  }\n\n  var _proto = Mutation.prototype;\n\n  _proto.setState = function setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state: state\n    });\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    this.observers = this.observers.filter(function (x) {\n      return x !== observer;\n    });\n  };\n\n  _proto.cancel = function cancel() {\n    if (this.retryer) {\n      this.retryer.cancel();\n      return this.retryer.promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop);\n    }\n\n    return Promise.resolve();\n  };\n\n  _proto.continue = function _continue() {\n    if (this.retryer) {\n      this.retryer.continue();\n      return this.retryer.promise;\n    }\n\n    return this.execute();\n  };\n\n  _proto.execute = function execute() {\n    var _this = this;\n\n    var data;\n    var restored = this.state.status === 'loading';\n    var promise = Promise.resolve();\n\n    if (!restored) {\n      this.dispatch({\n        type: 'loading',\n        variables: this.options.variables\n      });\n      promise = promise.then(function () {\n        // Notify cache callback\n        _this.mutationCache.config.onMutate == null ? void 0 : _this.mutationCache.config.onMutate(_this.state.variables, _this);\n      }).then(function () {\n        return _this.options.onMutate == null ? void 0 : _this.options.onMutate(_this.state.variables);\n      }).then(function (context) {\n        if (context !== _this.state.context) {\n          _this.dispatch({\n            type: 'loading',\n            context: context,\n            variables: _this.state.variables\n          });\n        }\n      });\n    }\n\n    return promise.then(function () {\n      return _this.executeMutation();\n    }).then(function (result) {\n      data = result; // Notify cache callback\n\n      _this.mutationCache.config.onSuccess == null ? void 0 : _this.mutationCache.config.onSuccess(data, _this.state.variables, _this.state.context, _this);\n    }).then(function () {\n      return _this.options.onSuccess == null ? void 0 : _this.options.onSuccess(data, _this.state.variables, _this.state.context);\n    }).then(function () {\n      return _this.options.onSettled == null ? void 0 : _this.options.onSettled(data, null, _this.state.variables, _this.state.context);\n    }).then(function () {\n      _this.dispatch({\n        type: 'success',\n        data: data\n      });\n\n      return data;\n    }).catch(function (error) {\n      // Notify cache callback\n      _this.mutationCache.config.onError == null ? void 0 : _this.mutationCache.config.onError(error, _this.state.variables, _this.state.context, _this); // Log error\n\n      (0,_logger__WEBPACK_IMPORTED_MODULE_2__.getLogger)().error(error);\n      return Promise.resolve().then(function () {\n        return _this.options.onError == null ? void 0 : _this.options.onError(error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        return _this.options.onSettled == null ? void 0 : _this.options.onSettled(undefined, error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        _this.dispatch({\n          type: 'error',\n          error: error\n        });\n\n        throw error;\n      });\n    });\n  };\n\n  _proto.executeMutation = function executeMutation() {\n    var _this2 = this,\n        _this$options$retry;\n\n    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_3__.Retryer({\n      fn: function fn() {\n        if (!_this2.options.mutationFn) {\n          return Promise.reject('No mutationFn found');\n        }\n\n        return _this2.options.mutationFn(_this2.state.variables);\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n      retryDelay: this.options.retryDelay\n    });\n    return this.retryer.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = reducer(this.state, action);\n    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onMutationUpdate(action);\n      });\n\n      _this3.mutationCache.notify(_this3);\n    });\n  };\n\n  return Mutation;\n}();\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'failed':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        failureCount: state.failureCount + 1\n      });\n\n    case 'pause':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        isPaused: true\n      });\n\n    case 'continue':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        isPaused: false\n      });\n\n    case 'loading':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables\n      });\n\n    case 'success':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false\n      });\n\n    case 'error':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error'\n      });\n\n    case 'setState':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, action.state);\n\n    default:\n      return state;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutationCache.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/mutationCache.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation */ \"(ssr)/./node_modules/react-query/es/core/mutation.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar MutationCache = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(MutationCache, _Subscribable);\n\n  function MutationCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n\n  var _proto = MutationCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var mutation = new _mutation__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(function (mutation) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);\n    });\n  };\n\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);\n    });\n  };\n\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(_utils__WEBPACK_IMPORTED_MODULE_3__.noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n\n  return MutationCache;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutationObserver.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-query/es/core/mutationObserver.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation */ \"(ssr)/./node_modules/react-query/es/core/mutation.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\n// CLASS\nvar MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(MutationObserver, _Subscribable);\n\n  function MutationObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n\n    _this.setOptions(options);\n\n    _this.bindMethods();\n\n    _this.updateResult();\n\n    return _this;\n  }\n\n  var _proto = MutationObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : (0,_mutation__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n\n    var result = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n\n    this.currentResult = result;\n  };\n\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n\n  return MutationObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/notifyManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/notifyManager.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotifyManager: () => (/* binding */ NotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n // TYPES\n\n// CLASS\nvar NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n\n    this.notifyFn = function (callback) {\n      callback();\n    };\n\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n\n  var _proto = NotifyManager.prototype;\n\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n\n    return result;\n  };\n\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  ;\n\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n\n  _proto.flush = function flush() {\n    var _this3 = this;\n\n    var queue = this.queue;\n    this.queue = [];\n\n    if (queue.length) {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  ;\n\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  ;\n\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n\n  return NotifyManager;\n}(); // SINGLETON\n\nvar notifyManager = new NotifyManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/onlineManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/onlineManager.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\nvar OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(OnlineManager, _Subscribable);\n\n  function OnlineManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onOnline) {\n      var _window;\n\n      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = OnlineManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n\n    if (online) {\n      this.onOnline();\n    }\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  };\n\n  return OnlineManager;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);\nvar onlineManager = new OnlineManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queriesObserver.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-query/es/core/queriesObserver.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueriesObserver: () => (/* binding */ QueriesObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\nvar QueriesObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(QueriesObserver, _Subscribable);\n\n  function QueriesObserver(client, queries) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.queries = [];\n    _this.result = [];\n    _this.observers = [];\n    _this.observersMap = {};\n\n    if (queries) {\n      _this.setQueries(queries);\n    }\n\n    return _this;\n  }\n\n  var _proto = QueriesObserver.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    var _this2 = this;\n\n    if (this.listeners.length === 1) {\n      this.observers.forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this2.onUpdate(observer, result);\n        });\n      });\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.observers.forEach(function (observer) {\n      observer.destroy();\n    });\n  };\n\n  _proto.setQueries = function setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    this.updateObservers(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.result;\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(function (match) {\n      return match.observer.getOptimisticResult(match.defaultedQueryOptions);\n    });\n  };\n\n  _proto.findMatchingObservers = function findMatchingObservers(queries) {\n    var _this3 = this;\n\n    var prevObservers = this.observers;\n    var defaultedQueryOptions = queries.map(function (options) {\n      return _this3.client.defaultQueryObserverOptions(options);\n    });\n    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {\n      var match = prevObservers.find(function (observer) {\n        return observer.options.queryHash === defaultedOptions.queryHash;\n      });\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    var matchedQueryHashes = matchingObservers.map(function (match) {\n      return match.defaultedQueryOptions.queryHash;\n    });\n    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {\n      return !matchedQueryHashes.includes(defaultedOptions.queryHash);\n    });\n    var unmatchedObservers = prevObservers.filter(function (prevObserver) {\n      return !matchingObservers.some(function (match) {\n        return match.observer === prevObserver;\n      });\n    });\n    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        var previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: _this3.getObserver(options)\n      };\n    });\n\n    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {\n      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n    };\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  };\n\n  _proto.getObserver = function getObserver(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var currentObserver = this.observersMap[defaultedOptions.queryHash];\n    return currentObserver != null ? currentObserver : new _queryObserver__WEBPACK_IMPORTED_MODULE_1__.QueryObserver(this.client, defaultedOptions);\n  };\n\n  _proto.updateObservers = function updateObservers(notifyOptions) {\n    var _this4 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      var prevObservers = _this4.observers;\n\n      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes\n\n\n      newObserverMatches.forEach(function (match) {\n        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);\n      });\n      var newObservers = newObserverMatches.map(function (match) {\n        return match.observer;\n      });\n      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {\n        return [observer.options.queryHash, observer];\n      }));\n      var newResult = newObservers.map(function (observer) {\n        return observer.getCurrentResult();\n      });\n      var hasIndexChange = newObservers.some(function (observer, index) {\n        return observer !== prevObservers[index];\n      });\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      _this4.observers = newObservers;\n      _this4.observersMap = newObserversMap;\n      _this4.result = newResult;\n\n      if (!_this4.hasListeners()) {\n        return;\n      }\n\n      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(prevObservers, newObservers).forEach(function (observer) {\n        observer.destroy();\n      });\n      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(newObservers, prevObservers).forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this4.onUpdate(observer, result);\n        });\n      });\n\n      _this4.notify();\n    });\n  };\n\n  _proto.onUpdate = function onUpdate(observer, result) {\n    var index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.replaceAt)(this.result, index, result);\n      this.notify();\n    }\n  };\n\n  _proto.notify = function notify() {\n    var _this5 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this5.listeners.forEach(function (listener) {\n        listener(_this5.result);\n      });\n    });\n  };\n\n  return QueriesObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queriesObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/query.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/query.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n\n  var _proto = Query.prototype;\n\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n\n    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n\n    this.clearGcTimeout();\n\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n\n    var prevData = this.state.data; // Get the new data\n\n    var data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate)(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.replaceEqualDeep)(prevData, data);\n    } // Set data and mark it as cached\n\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  };\n\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !(0,_utils__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n        _this$options$behavio,\n        _context$fetchOptions,\n        _abortController$abor;\n\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    var queryKey = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.ensureQueryKeyArray)(this.queryKey);\n    var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAbortController)(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n\n    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_2__.Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n\n        if (!(0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          (0,_logger__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(error);\n        } // Remove query after fetching if cache time is 0\n\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = this.reducer(this.state, action);\n    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n\n    switch (action.type) {\n      case 'failed':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n\n      case 'pause':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isPaused: true\n        });\n\n      case 'continue':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isPaused: false\n        });\n\n      case 'fetch':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n\n      case 'success':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n\n      case 'error':\n        var error = action.error;\n\n        if ((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.revertState) {\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.revertState);\n        }\n\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n\n      case 'invalidate':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isInvalidated: true\n        });\n\n      case 'setState':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, action.state);\n\n      default:\n        return state;\n    }\n  };\n\n  return Query;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9xdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEQ7QUFDa0Y7QUFDNUY7QUFDWDtBQUNpQixDQUFDOztBQUV2RDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBLG1CQUFtQiw4RUFBUSxHQUFHO0FBQzlCLHlEQUF5RDs7QUFFekQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQSxRQUFRLHNEQUFjO0FBQ3RCO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsb0NBQW9DOztBQUVwQyxlQUFlLHdEQUFnQixxQkFBcUI7O0FBRXBEO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxhQUFhLHdEQUFnQjtBQUM3QixNQUFNOzs7QUFHTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0NBQWtDLHdDQUFJLFFBQVEsd0NBQUk7QUFDbEQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEscUVBQXFFLHNEQUFjO0FBQ25GOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBLE1BQU07OztBQUdOO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0EsTUFBTTs7O0FBR047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7O0FBRWhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsWUFBWTtBQUNaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7O0FBRUE7QUFDQSwyRkFBMkY7O0FBRTNGO0FBQ0E7QUFDQSxNQUFNOzs7QUFHTjtBQUNBO0FBQ0EsTUFBTTtBQUNOOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1CQUFtQiwyREFBbUI7QUFDdEMsMEJBQTBCLDBEQUFrQixJQUFJOztBQUVoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEtBQUssR0FBRzs7QUFFUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsT0FBTzs7O0FBR1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsTUFBTTs7O0FBR04sbUNBQW1DOztBQUVuQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNOzs7QUFHTix1QkFBdUIsNkNBQU87QUFDOUI7QUFDQTtBQUNBO0FBQ0EsOEJBQThCOzs7QUFHOUIsc0dBQXNHOztBQUV0RztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLGNBQWMsMERBQWdCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDs7QUFFQSxhQUFhLDBEQUFnQjtBQUM3QjtBQUNBLHFHQUFxRzs7QUFFckcsVUFBVSxrREFBUztBQUNuQixVQUFVOzs7QUFHVjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLElBQUkseURBQWE7QUFDakI7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZUFBZSw4RUFBUSxHQUFHO0FBQzFCO0FBQ0EsU0FBUzs7QUFFVDtBQUNBLGVBQWUsOEVBQVEsR0FBRztBQUMxQjtBQUNBLFNBQVM7O0FBRVQ7QUFDQSxlQUFlLDhFQUFRLEdBQUc7QUFDMUI7QUFDQSxTQUFTOztBQUVUO0FBQ0EsZUFBZSw4RUFBUSxHQUFHO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTOztBQUVUO0FBQ0EsZUFBZSw4RUFBUSxHQUFHO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTs7QUFFQSxZQUFZLDBEQUFnQjtBQUM1QixpQkFBaUIsOEVBQVEsR0FBRztBQUM1Qjs7QUFFQSxlQUFlLDhFQUFRLEdBQUc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTOztBQUVUO0FBQ0EsZUFBZSw4RUFBUSxHQUFHO0FBQzFCO0FBQ0EsU0FBUzs7QUFFVDtBQUNBLGVBQWUsOEVBQVEsR0FBRzs7QUFFMUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9xdWVyeS5qcz9hNDFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IHsgZ2V0QWJvcnRDb250cm9sbGVyLCBmdW5jdGlvbmFsVXBkYXRlLCBpc1ZhbGlkVGltZW91dCwgbm9vcCwgcmVwbGFjZUVxdWFsRGVlcCwgdGltZVVudGlsU3RhbGUsIGVuc3VyZVF1ZXJ5S2V5QXJyYXkgfSBmcm9tICcuL3V0aWxzJztcbmltcG9ydCB7IG5vdGlmeU1hbmFnZXIgfSBmcm9tICcuL25vdGlmeU1hbmFnZXInO1xuaW1wb3J0IHsgZ2V0TG9nZ2VyIH0gZnJvbSAnLi9sb2dnZXInO1xuaW1wb3J0IHsgUmV0cnllciwgaXNDYW5jZWxsZWRFcnJvciB9IGZyb20gJy4vcmV0cnllcic7IC8vIFRZUEVTXG5cbi8vIENMQVNTXG5leHBvcnQgdmFyIFF1ZXJ5ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgZnVuY3Rpb24gUXVlcnkoY29uZmlnKSB7XG4gICAgdGhpcy5hYm9ydFNpZ25hbENvbnN1bWVkID0gZmFsc2U7XG4gICAgdGhpcy5oYWRPYnNlcnZlcnMgPSBmYWxzZTtcbiAgICB0aGlzLmRlZmF1bHRPcHRpb25zID0gY29uZmlnLmRlZmF1bHRPcHRpb25zO1xuICAgIHRoaXMuc2V0T3B0aW9ucyhjb25maWcub3B0aW9ucyk7XG4gICAgdGhpcy5vYnNlcnZlcnMgPSBbXTtcbiAgICB0aGlzLmNhY2hlID0gY29uZmlnLmNhY2hlO1xuICAgIHRoaXMucXVlcnlLZXkgPSBjb25maWcucXVlcnlLZXk7XG4gICAgdGhpcy5xdWVyeUhhc2ggPSBjb25maWcucXVlcnlIYXNoO1xuICAgIHRoaXMuaW5pdGlhbFN0YXRlID0gY29uZmlnLnN0YXRlIHx8IHRoaXMuZ2V0RGVmYXVsdFN0YXRlKHRoaXMub3B0aW9ucyk7XG4gICAgdGhpcy5zdGF0ZSA9IHRoaXMuaW5pdGlhbFN0YXRlO1xuICAgIHRoaXMubWV0YSA9IGNvbmZpZy5tZXRhO1xuICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICB9XG5cbiAgdmFyIF9wcm90byA9IFF1ZXJ5LnByb3RvdHlwZTtcblxuICBfcHJvdG8uc2V0T3B0aW9ucyA9IGZ1bmN0aW9uIHNldE9wdGlvbnMob3B0aW9ucykge1xuICAgIHZhciBfdGhpcyRvcHRpb25zJGNhY2hlVGk7XG5cbiAgICB0aGlzLm9wdGlvbnMgPSBfZXh0ZW5kcyh7fSwgdGhpcy5kZWZhdWx0T3B0aW9ucywgb3B0aW9ucyk7XG4gICAgdGhpcy5tZXRhID0gb3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5tZXRhOyAvLyBEZWZhdWx0IHRvIDUgbWludXRlcyBpZiBub3QgY2FjaGUgdGltZSBpcyBzZXRcblxuICAgIHRoaXMuY2FjaGVUaW1lID0gTWF0aC5tYXgodGhpcy5jYWNoZVRpbWUgfHwgMCwgKF90aGlzJG9wdGlvbnMkY2FjaGVUaSA9IHRoaXMub3B0aW9ucy5jYWNoZVRpbWUpICE9IG51bGwgPyBfdGhpcyRvcHRpb25zJGNhY2hlVGkgOiA1ICogNjAgKiAxMDAwKTtcbiAgfTtcblxuICBfcHJvdG8uc2V0RGVmYXVsdE9wdGlvbnMgPSBmdW5jdGlvbiBzZXREZWZhdWx0T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgdGhpcy5kZWZhdWx0T3B0aW9ucyA9IG9wdGlvbnM7XG4gIH07XG5cbiAgX3Byb3RvLnNjaGVkdWxlR2MgPSBmdW5jdGlvbiBzY2hlZHVsZUdjKCkge1xuICAgIHZhciBfdGhpcyA9IHRoaXM7XG5cbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG5cbiAgICBpZiAoaXNWYWxpZFRpbWVvdXQodGhpcy5jYWNoZVRpbWUpKSB7XG4gICAgICB0aGlzLmdjVGltZW91dCA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgICBfdGhpcy5vcHRpb25hbFJlbW92ZSgpO1xuICAgICAgfSwgdGhpcy5jYWNoZVRpbWUpO1xuICAgIH1cbiAgfTtcblxuICBfcHJvdG8uY2xlYXJHY1RpbWVvdXQgPSBmdW5jdGlvbiBjbGVhckdjVGltZW91dCgpIHtcbiAgICBpZiAodGhpcy5nY1RpbWVvdXQpIHtcbiAgICAgIGNsZWFyVGltZW91dCh0aGlzLmdjVGltZW91dCk7XG4gICAgICB0aGlzLmdjVGltZW91dCA9IHVuZGVmaW5lZDtcbiAgICB9XG4gIH07XG5cbiAgX3Byb3RvLm9wdGlvbmFsUmVtb3ZlID0gZnVuY3Rpb24gb3B0aW9uYWxSZW1vdmUoKSB7XG4gICAgaWYgKCF0aGlzLm9ic2VydmVycy5sZW5ndGgpIHtcbiAgICAgIGlmICh0aGlzLnN0YXRlLmlzRmV0Y2hpbmcpIHtcbiAgICAgICAgaWYgKHRoaXMuaGFkT2JzZXJ2ZXJzKSB7XG4gICAgICAgICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMuY2FjaGUucmVtb3ZlKHRoaXMpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBfcHJvdG8uc2V0RGF0YSA9IGZ1bmN0aW9uIHNldERhdGEodXBkYXRlciwgb3B0aW9ucykge1xuICAgIHZhciBfdGhpcyRvcHRpb25zJGlzRGF0YUUsIF90aGlzJG9wdGlvbnM7XG5cbiAgICB2YXIgcHJldkRhdGEgPSB0aGlzLnN0YXRlLmRhdGE7IC8vIEdldCB0aGUgbmV3IGRhdGFcblxuICAgIHZhciBkYXRhID0gZnVuY3Rpb25hbFVwZGF0ZSh1cGRhdGVyLCBwcmV2RGF0YSk7IC8vIFVzZSBwcmV2IGRhdGEgaWYgYW4gaXNEYXRhRXF1YWwgZnVuY3Rpb24gaXMgZGVmaW5lZCBhbmQgcmV0dXJucyBgdHJ1ZWBcblxuICAgIGlmICgoX3RoaXMkb3B0aW9ucyRpc0RhdGFFID0gKF90aGlzJG9wdGlvbnMgPSB0aGlzLm9wdGlvbnMpLmlzRGF0YUVxdWFsKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMkb3B0aW9ucyRpc0RhdGFFLmNhbGwoX3RoaXMkb3B0aW9ucywgcHJldkRhdGEsIGRhdGEpKSB7XG4gICAgICBkYXRhID0gcHJldkRhdGE7XG4gICAgfSBlbHNlIGlmICh0aGlzLm9wdGlvbnMuc3RydWN0dXJhbFNoYXJpbmcgIT09IGZhbHNlKSB7XG4gICAgICAvLyBTdHJ1Y3R1cmFsbHkgc2hhcmUgZGF0YSBiZXR3ZWVuIHByZXYgYW5kIG5ldyBkYXRhIGlmIG5lZWRlZFxuICAgICAgZGF0YSA9IHJlcGxhY2VFcXVhbERlZXAocHJldkRhdGEsIGRhdGEpO1xuICAgIH0gLy8gU2V0IGRhdGEgYW5kIG1hcmsgaXQgYXMgY2FjaGVkXG5cblxuICAgIHRoaXMuZGlzcGF0Y2goe1xuICAgICAgZGF0YTogZGF0YSxcbiAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgIGRhdGFVcGRhdGVkQXQ6IG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMudXBkYXRlZEF0XG4gICAgfSk7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH07XG5cbiAgX3Byb3RvLnNldFN0YXRlID0gZnVuY3Rpb24gc2V0U3RhdGUoc3RhdGUsIHNldFN0YXRlT3B0aW9ucykge1xuICAgIHRoaXMuZGlzcGF0Y2goe1xuICAgICAgdHlwZTogJ3NldFN0YXRlJyxcbiAgICAgIHN0YXRlOiBzdGF0ZSxcbiAgICAgIHNldFN0YXRlT3B0aW9uczogc2V0U3RhdGVPcHRpb25zXG4gICAgfSk7XG4gIH07XG5cbiAgX3Byb3RvLmNhbmNlbCA9IGZ1bmN0aW9uIGNhbmNlbChvcHRpb25zKSB7XG4gICAgdmFyIF90aGlzJHJldHJ5ZXI7XG5cbiAgICB2YXIgcHJvbWlzZSA9IHRoaXMucHJvbWlzZTtcbiAgICAoX3RoaXMkcmV0cnllciA9IHRoaXMucmV0cnllcikgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJHJldHJ5ZXIuY2FuY2VsKG9wdGlvbnMpO1xuICAgIHJldHVybiBwcm9taXNlID8gcHJvbWlzZS50aGVuKG5vb3ApLmNhdGNoKG5vb3ApIDogUHJvbWlzZS5yZXNvbHZlKCk7XG4gIH07XG5cbiAgX3Byb3RvLmRlc3Ryb3kgPSBmdW5jdGlvbiBkZXN0cm95KCkge1xuICAgIHRoaXMuY2xlYXJHY1RpbWVvdXQoKTtcbiAgICB0aGlzLmNhbmNlbCh7XG4gICAgICBzaWxlbnQ6IHRydWVcbiAgICB9KTtcbiAgfTtcblxuICBfcHJvdG8ucmVzZXQgPSBmdW5jdGlvbiByZXNldCgpIHtcbiAgICB0aGlzLmRlc3Ryb3koKTtcbiAgICB0aGlzLnNldFN0YXRlKHRoaXMuaW5pdGlhbFN0YXRlKTtcbiAgfTtcblxuICBfcHJvdG8uaXNBY3RpdmUgPSBmdW5jdGlvbiBpc0FjdGl2ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5vYnNlcnZlcnMuc29tZShmdW5jdGlvbiAob2JzZXJ2ZXIpIHtcbiAgICAgIHJldHVybiBvYnNlcnZlci5vcHRpb25zLmVuYWJsZWQgIT09IGZhbHNlO1xuICAgIH0pO1xuICB9O1xuXG4gIF9wcm90by5pc0ZldGNoaW5nID0gZnVuY3Rpb24gaXNGZXRjaGluZygpIHtcbiAgICByZXR1cm4gdGhpcy5zdGF0ZS5pc0ZldGNoaW5nO1xuICB9O1xuXG4gIF9wcm90by5pc1N0YWxlID0gZnVuY3Rpb24gaXNTdGFsZSgpIHtcbiAgICByZXR1cm4gdGhpcy5zdGF0ZS5pc0ludmFsaWRhdGVkIHx8ICF0aGlzLnN0YXRlLmRhdGFVcGRhdGVkQXQgfHwgdGhpcy5vYnNlcnZlcnMuc29tZShmdW5jdGlvbiAob2JzZXJ2ZXIpIHtcbiAgICAgIHJldHVybiBvYnNlcnZlci5nZXRDdXJyZW50UmVzdWx0KCkuaXNTdGFsZTtcbiAgICB9KTtcbiAgfTtcblxuICBfcHJvdG8uaXNTdGFsZUJ5VGltZSA9IGZ1bmN0aW9uIGlzU3RhbGVCeVRpbWUoc3RhbGVUaW1lKSB7XG4gICAgaWYgKHN0YWxlVGltZSA9PT0gdm9pZCAwKSB7XG4gICAgICBzdGFsZVRpbWUgPSAwO1xuICAgIH1cblxuICAgIHJldHVybiB0aGlzLnN0YXRlLmlzSW52YWxpZGF0ZWQgfHwgIXRoaXMuc3RhdGUuZGF0YVVwZGF0ZWRBdCB8fCAhdGltZVVudGlsU3RhbGUodGhpcy5zdGF0ZS5kYXRhVXBkYXRlZEF0LCBzdGFsZVRpbWUpO1xuICB9O1xuXG4gIF9wcm90by5vbkZvY3VzID0gZnVuY3Rpb24gb25Gb2N1cygpIHtcbiAgICB2YXIgX3RoaXMkcmV0cnllcjI7XG5cbiAgICB2YXIgb2JzZXJ2ZXIgPSB0aGlzLm9ic2VydmVycy5maW5kKGZ1bmN0aW9uICh4KSB7XG4gICAgICByZXR1cm4geC5zaG91bGRGZXRjaE9uV2luZG93Rm9jdXMoKTtcbiAgICB9KTtcblxuICAgIGlmIChvYnNlcnZlcikge1xuICAgICAgb2JzZXJ2ZXIucmVmZXRjaCgpO1xuICAgIH0gLy8gQ29udGludWUgZmV0Y2ggaWYgY3VycmVudGx5IHBhdXNlZFxuXG5cbiAgICAoX3RoaXMkcmV0cnllcjIgPSB0aGlzLnJldHJ5ZXIpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRyZXRyeWVyMi5jb250aW51ZSgpO1xuICB9O1xuXG4gIF9wcm90by5vbk9ubGluZSA9IGZ1bmN0aW9uIG9uT25saW5lKCkge1xuICAgIHZhciBfdGhpcyRyZXRyeWVyMztcblxuICAgIHZhciBvYnNlcnZlciA9IHRoaXMub2JzZXJ2ZXJzLmZpbmQoZnVuY3Rpb24gKHgpIHtcbiAgICAgIHJldHVybiB4LnNob3VsZEZldGNoT25SZWNvbm5lY3QoKTtcbiAgICB9KTtcblxuICAgIGlmIChvYnNlcnZlcikge1xuICAgICAgb2JzZXJ2ZXIucmVmZXRjaCgpO1xuICAgIH0gLy8gQ29udGludWUgZmV0Y2ggaWYgY3VycmVudGx5IHBhdXNlZFxuXG5cbiAgICAoX3RoaXMkcmV0cnllcjMgPSB0aGlzLnJldHJ5ZXIpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRyZXRyeWVyMy5jb250aW51ZSgpO1xuICB9O1xuXG4gIF9wcm90by5hZGRPYnNlcnZlciA9IGZ1bmN0aW9uIGFkZE9ic2VydmVyKG9ic2VydmVyKSB7XG4gICAgaWYgKHRoaXMub2JzZXJ2ZXJzLmluZGV4T2Yob2JzZXJ2ZXIpID09PSAtMSkge1xuICAgICAgdGhpcy5vYnNlcnZlcnMucHVzaChvYnNlcnZlcik7XG4gICAgICB0aGlzLmhhZE9ic2VydmVycyA9IHRydWU7IC8vIFN0b3AgdGhlIHF1ZXJ5IGZyb20gYmVpbmcgZ2FyYmFnZSBjb2xsZWN0ZWRcblxuICAgICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgICAgdGhpcy5jYWNoZS5ub3RpZnkoe1xuICAgICAgICB0eXBlOiAnb2JzZXJ2ZXJBZGRlZCcsXG4gICAgICAgIHF1ZXJ5OiB0aGlzLFxuICAgICAgICBvYnNlcnZlcjogb2JzZXJ2ZXJcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBfcHJvdG8ucmVtb3ZlT2JzZXJ2ZXIgPSBmdW5jdGlvbiByZW1vdmVPYnNlcnZlcihvYnNlcnZlcikge1xuICAgIGlmICh0aGlzLm9ic2VydmVycy5pbmRleE9mKG9ic2VydmVyKSAhPT0gLTEpIHtcbiAgICAgIHRoaXMub2JzZXJ2ZXJzID0gdGhpcy5vYnNlcnZlcnMuZmlsdGVyKGZ1bmN0aW9uICh4KSB7XG4gICAgICAgIHJldHVybiB4ICE9PSBvYnNlcnZlcjtcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXRoaXMub2JzZXJ2ZXJzLmxlbmd0aCkge1xuICAgICAgICAvLyBJZiB0aGUgdHJhbnNwb3J0IGxheWVyIGRvZXMgbm90IHN1cHBvcnQgY2FuY2VsbGF0aW9uXG4gICAgICAgIC8vIHdlJ2xsIGxldCB0aGUgcXVlcnkgY29udGludWUgc28gdGhlIHJlc3VsdCBjYW4gYmUgY2FjaGVkXG4gICAgICAgIGlmICh0aGlzLnJldHJ5ZXIpIHtcbiAgICAgICAgICBpZiAodGhpcy5yZXRyeWVyLmlzVHJhbnNwb3J0Q2FuY2VsYWJsZSB8fCB0aGlzLmFib3J0U2lnbmFsQ29uc3VtZWQpIHtcbiAgICAgICAgICAgIHRoaXMucmV0cnllci5jYW5jZWwoe1xuICAgICAgICAgICAgICByZXZlcnQ6IHRydWVcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnJldHJ5ZXIuY2FuY2VsUmV0cnkoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodGhpcy5jYWNoZVRpbWUpIHtcbiAgICAgICAgICB0aGlzLnNjaGVkdWxlR2MoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aGlzLmNhY2hlLnJlbW92ZSh0aGlzKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICB0aGlzLmNhY2hlLm5vdGlmeSh7XG4gICAgICAgIHR5cGU6ICdvYnNlcnZlclJlbW92ZWQnLFxuICAgICAgICBxdWVyeTogdGhpcyxcbiAgICAgICAgb2JzZXJ2ZXI6IG9ic2VydmVyXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgX3Byb3RvLmdldE9ic2VydmVyc0NvdW50ID0gZnVuY3Rpb24gZ2V0T2JzZXJ2ZXJzQ291bnQoKSB7XG4gICAgcmV0dXJuIHRoaXMub2JzZXJ2ZXJzLmxlbmd0aDtcbiAgfTtcblxuICBfcHJvdG8uaW52YWxpZGF0ZSA9IGZ1bmN0aW9uIGludmFsaWRhdGUoKSB7XG4gICAgaWYgKCF0aGlzLnN0YXRlLmlzSW52YWxpZGF0ZWQpIHtcbiAgICAgIHRoaXMuZGlzcGF0Y2goe1xuICAgICAgICB0eXBlOiAnaW52YWxpZGF0ZSdcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBfcHJvdG8uZmV0Y2ggPSBmdW5jdGlvbiBmZXRjaChvcHRpb25zLCBmZXRjaE9wdGlvbnMpIHtcbiAgICB2YXIgX3RoaXMyID0gdGhpcyxcbiAgICAgICAgX3RoaXMkb3B0aW9ucyRiZWhhdmlvLFxuICAgICAgICBfY29udGV4dCRmZXRjaE9wdGlvbnMsXG4gICAgICAgIF9hYm9ydENvbnRyb2xsZXIkYWJvcjtcblxuICAgIGlmICh0aGlzLnN0YXRlLmlzRmV0Y2hpbmcpIHtcbiAgICAgIGlmICh0aGlzLnN0YXRlLmRhdGFVcGRhdGVkQXQgJiYgKGZldGNoT3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogZmV0Y2hPcHRpb25zLmNhbmNlbFJlZmV0Y2gpKSB7XG4gICAgICAgIC8vIFNpbGVudGx5IGNhbmNlbCBjdXJyZW50IGZldGNoIGlmIHRoZSB1c2VyIHdhbnRzIHRvIGNhbmNlbCByZWZldGNoZXNcbiAgICAgICAgdGhpcy5jYW5jZWwoe1xuICAgICAgICAgIHNpbGVudDogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSBpZiAodGhpcy5wcm9taXNlKSB7XG4gICAgICAgIHZhciBfdGhpcyRyZXRyeWVyNDtcblxuICAgICAgICAvLyBtYWtlIHN1cmUgdGhhdCByZXRyaWVzIHRoYXQgd2VyZSBwb3RlbnRpYWxseSBjYW5jZWxsZWQgZHVlIHRvIHVubW91bnRzIGNhbiBjb250aW51ZVxuICAgICAgICAoX3RoaXMkcmV0cnllcjQgPSB0aGlzLnJldHJ5ZXIpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRyZXRyeWVyNC5jb250aW51ZVJldHJ5KCk7IC8vIFJldHVybiBjdXJyZW50IHByb21pc2UgaWYgd2UgYXJlIGFscmVhZHkgZmV0Y2hpbmdcblxuICAgICAgICByZXR1cm4gdGhpcy5wcm9taXNlO1xuICAgICAgfVxuICAgIH0gLy8gVXBkYXRlIGNvbmZpZyBpZiBwYXNzZWQsIG90aGVyd2lzZSB0aGUgY29uZmlnIGZyb20gdGhlIGxhc3QgZXhlY3V0aW9uIGlzIHVzZWRcblxuXG4gICAgaWYgKG9wdGlvbnMpIHtcbiAgICAgIHRoaXMuc2V0T3B0aW9ucyhvcHRpb25zKTtcbiAgICB9IC8vIFVzZSB0aGUgb3B0aW9ucyBmcm9tIHRoZSBmaXJzdCBvYnNlcnZlciB3aXRoIGEgcXVlcnkgZnVuY3Rpb24gaWYgbm8gZnVuY3Rpb24gaXMgZm91bmQuXG4gICAgLy8gVGhpcyBjYW4gaGFwcGVuIHdoZW4gdGhlIHF1ZXJ5IGlzIGh5ZHJhdGVkIG9yIGNyZWF0ZWQgd2l0aCBzZXRRdWVyeURhdGEuXG5cblxuICAgIGlmICghdGhpcy5vcHRpb25zLnF1ZXJ5Rm4pIHtcbiAgICAgIHZhciBvYnNlcnZlciA9IHRoaXMub2JzZXJ2ZXJzLmZpbmQoZnVuY3Rpb24gKHgpIHtcbiAgICAgICAgcmV0dXJuIHgub3B0aW9ucy5xdWVyeUZuO1xuICAgICAgfSk7XG5cbiAgICAgIGlmIChvYnNlcnZlcikge1xuICAgICAgICB0aGlzLnNldE9wdGlvbnMob2JzZXJ2ZXIub3B0aW9ucyk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgdmFyIHF1ZXJ5S2V5ID0gZW5zdXJlUXVlcnlLZXlBcnJheSh0aGlzLnF1ZXJ5S2V5KTtcbiAgICB2YXIgYWJvcnRDb250cm9sbGVyID0gZ2V0QWJvcnRDb250cm9sbGVyKCk7IC8vIENyZWF0ZSBxdWVyeSBmdW5jdGlvbiBjb250ZXh0XG5cbiAgICB2YXIgcXVlcnlGbkNvbnRleHQgPSB7XG4gICAgICBxdWVyeUtleTogcXVlcnlLZXksXG4gICAgICBwYWdlUGFyYW06IHVuZGVmaW5lZCxcbiAgICAgIG1ldGE6IHRoaXMubWV0YVxuICAgIH07XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHF1ZXJ5Rm5Db250ZXh0LCAnc2lnbmFsJywge1xuICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkge1xuICAgICAgICBpZiAoYWJvcnRDb250cm9sbGVyKSB7XG4gICAgICAgICAgX3RoaXMyLmFib3J0U2lnbmFsQ29uc3VtZWQgPSB0cnVlO1xuICAgICAgICAgIHJldHVybiBhYm9ydENvbnRyb2xsZXIuc2lnbmFsO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICAgIH1cbiAgICB9KTsgLy8gQ3JlYXRlIGZldGNoIGZ1bmN0aW9uXG5cbiAgICB2YXIgZmV0Y2hGbiA9IGZ1bmN0aW9uIGZldGNoRm4oKSB7XG4gICAgICBpZiAoIV90aGlzMi5vcHRpb25zLnF1ZXJ5Rm4pIHtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KCdNaXNzaW5nIHF1ZXJ5Rm4nKTtcbiAgICAgIH1cblxuICAgICAgX3RoaXMyLmFib3J0U2lnbmFsQ29uc3VtZWQgPSBmYWxzZTtcbiAgICAgIHJldHVybiBfdGhpczIub3B0aW9ucy5xdWVyeUZuKHF1ZXJ5Rm5Db250ZXh0KTtcbiAgICB9OyAvLyBUcmlnZ2VyIGJlaGF2aW9yIGhvb2tcblxuXG4gICAgdmFyIGNvbnRleHQgPSB7XG4gICAgICBmZXRjaE9wdGlvbnM6IGZldGNoT3B0aW9ucyxcbiAgICAgIG9wdGlvbnM6IHRoaXMub3B0aW9ucyxcbiAgICAgIHF1ZXJ5S2V5OiBxdWVyeUtleSxcbiAgICAgIHN0YXRlOiB0aGlzLnN0YXRlLFxuICAgICAgZmV0Y2hGbjogZmV0Y2hGbixcbiAgICAgIG1ldGE6IHRoaXMubWV0YVxuICAgIH07XG5cbiAgICBpZiAoKF90aGlzJG9wdGlvbnMkYmVoYXZpbyA9IHRoaXMub3B0aW9ucy5iZWhhdmlvcikgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJG9wdGlvbnMkYmVoYXZpby5vbkZldGNoKSB7XG4gICAgICB2YXIgX3RoaXMkb3B0aW9ucyRiZWhhdmlvMjtcblxuICAgICAgKF90aGlzJG9wdGlvbnMkYmVoYXZpbzIgPSB0aGlzLm9wdGlvbnMuYmVoYXZpb3IpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRvcHRpb25zJGJlaGF2aW8yLm9uRmV0Y2goY29udGV4dCk7XG4gICAgfSAvLyBTdG9yZSBzdGF0ZSBpbiBjYXNlIHRoZSBjdXJyZW50IGZldGNoIG5lZWRzIHRvIGJlIHJldmVydGVkXG5cblxuICAgIHRoaXMucmV2ZXJ0U3RhdGUgPSB0aGlzLnN0YXRlOyAvLyBTZXQgdG8gZmV0Y2hpbmcgc3RhdGUgaWYgbm90IGFscmVhZHkgaW4gaXRcblxuICAgIGlmICghdGhpcy5zdGF0ZS5pc0ZldGNoaW5nIHx8IHRoaXMuc3RhdGUuZmV0Y2hNZXRhICE9PSAoKF9jb250ZXh0JGZldGNoT3B0aW9ucyA9IGNvbnRleHQuZmV0Y2hPcHRpb25zKSA9PSBudWxsID8gdm9pZCAwIDogX2NvbnRleHQkZmV0Y2hPcHRpb25zLm1ldGEpKSB7XG4gICAgICB2YXIgX2NvbnRleHQkZmV0Y2hPcHRpb25zMjtcblxuICAgICAgdGhpcy5kaXNwYXRjaCh7XG4gICAgICAgIHR5cGU6ICdmZXRjaCcsXG4gICAgICAgIG1ldGE6IChfY29udGV4dCRmZXRjaE9wdGlvbnMyID0gY29udGV4dC5mZXRjaE9wdGlvbnMpID09IG51bGwgPyB2b2lkIDAgOiBfY29udGV4dCRmZXRjaE9wdGlvbnMyLm1ldGFcbiAgICAgIH0pO1xuICAgIH0gLy8gVHJ5IHRvIGZldGNoIHRoZSBkYXRhXG5cblxuICAgIHRoaXMucmV0cnllciA9IG5ldyBSZXRyeWVyKHtcbiAgICAgIGZuOiBjb250ZXh0LmZldGNoRm4sXG4gICAgICBhYm9ydDogYWJvcnRDb250cm9sbGVyID09IG51bGwgPyB2b2lkIDAgOiAoX2Fib3J0Q29udHJvbGxlciRhYm9yID0gYWJvcnRDb250cm9sbGVyLmFib3J0KSA9PSBudWxsID8gdm9pZCAwIDogX2Fib3J0Q29udHJvbGxlciRhYm9yLmJpbmQoYWJvcnRDb250cm9sbGVyKSxcbiAgICAgIG9uU3VjY2VzczogZnVuY3Rpb24gb25TdWNjZXNzKGRhdGEpIHtcbiAgICAgICAgX3RoaXMyLnNldERhdGEoZGF0YSk7IC8vIE5vdGlmeSBjYWNoZSBjYWxsYmFja1xuXG5cbiAgICAgICAgX3RoaXMyLmNhY2hlLmNvbmZpZy5vblN1Y2Nlc3MgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzMi5jYWNoZS5jb25maWcub25TdWNjZXNzKGRhdGEsIF90aGlzMik7IC8vIFJlbW92ZSBxdWVyeSBhZnRlciBmZXRjaGluZyBpZiBjYWNoZSB0aW1lIGlzIDBcblxuICAgICAgICBpZiAoX3RoaXMyLmNhY2hlVGltZSA9PT0gMCkge1xuICAgICAgICAgIF90aGlzMi5vcHRpb25hbFJlbW92ZSgpO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgb25FcnJvcjogZnVuY3Rpb24gb25FcnJvcihlcnJvcikge1xuICAgICAgICAvLyBPcHRpbWlzdGljYWxseSB1cGRhdGUgc3RhdGUgaWYgbmVlZGVkXG4gICAgICAgIGlmICghKGlzQ2FuY2VsbGVkRXJyb3IoZXJyb3IpICYmIGVycm9yLnNpbGVudCkpIHtcbiAgICAgICAgICBfdGhpczIuZGlzcGF0Y2goe1xuICAgICAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgICAgIGVycm9yOiBlcnJvclxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFpc0NhbmNlbGxlZEVycm9yKGVycm9yKSkge1xuICAgICAgICAgIC8vIE5vdGlmeSBjYWNoZSBjYWxsYmFja1xuICAgICAgICAgIF90aGlzMi5jYWNoZS5jb25maWcub25FcnJvciA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMyLmNhY2hlLmNvbmZpZy5vbkVycm9yKGVycm9yLCBfdGhpczIpOyAvLyBMb2cgZXJyb3JcblxuICAgICAgICAgIGdldExvZ2dlcigpLmVycm9yKGVycm9yKTtcbiAgICAgICAgfSAvLyBSZW1vdmUgcXVlcnkgYWZ0ZXIgZmV0Y2hpbmcgaWYgY2FjaGUgdGltZSBpcyAwXG5cblxuICAgICAgICBpZiAoX3RoaXMyLmNhY2hlVGltZSA9PT0gMCkge1xuICAgICAgICAgIF90aGlzMi5vcHRpb25hbFJlbW92ZSgpO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgb25GYWlsOiBmdW5jdGlvbiBvbkZhaWwoKSB7XG4gICAgICAgIF90aGlzMi5kaXNwYXRjaCh7XG4gICAgICAgICAgdHlwZTogJ2ZhaWxlZCdcbiAgICAgICAgfSk7XG4gICAgICB9LFxuICAgICAgb25QYXVzZTogZnVuY3Rpb24gb25QYXVzZSgpIHtcbiAgICAgICAgX3RoaXMyLmRpc3BhdGNoKHtcbiAgICAgICAgICB0eXBlOiAncGF1c2UnXG4gICAgICAgIH0pO1xuICAgICAgfSxcbiAgICAgIG9uQ29udGludWU6IGZ1bmN0aW9uIG9uQ29udGludWUoKSB7XG4gICAgICAgIF90aGlzMi5kaXNwYXRjaCh7XG4gICAgICAgICAgdHlwZTogJ2NvbnRpbnVlJ1xuICAgICAgICB9KTtcbiAgICAgIH0sXG4gICAgICByZXRyeTogY29udGV4dC5vcHRpb25zLnJldHJ5LFxuICAgICAgcmV0cnlEZWxheTogY29udGV4dC5vcHRpb25zLnJldHJ5RGVsYXlcbiAgICB9KTtcbiAgICB0aGlzLnByb21pc2UgPSB0aGlzLnJldHJ5ZXIucHJvbWlzZTtcbiAgICByZXR1cm4gdGhpcy5wcm9taXNlO1xuICB9O1xuXG4gIF9wcm90by5kaXNwYXRjaCA9IGZ1bmN0aW9uIGRpc3BhdGNoKGFjdGlvbikge1xuICAgIHZhciBfdGhpczMgPSB0aGlzO1xuXG4gICAgdGhpcy5zdGF0ZSA9IHRoaXMucmVkdWNlcih0aGlzLnN0YXRlLCBhY3Rpb24pO1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goZnVuY3Rpb24gKCkge1xuICAgICAgX3RoaXMzLm9ic2VydmVycy5mb3JFYWNoKGZ1bmN0aW9uIChvYnNlcnZlcikge1xuICAgICAgICBvYnNlcnZlci5vblF1ZXJ5VXBkYXRlKGFjdGlvbik7XG4gICAgICB9KTtcblxuICAgICAgX3RoaXMzLmNhY2hlLm5vdGlmeSh7XG4gICAgICAgIHF1ZXJ5OiBfdGhpczMsXG4gICAgICAgIHR5cGU6ICdxdWVyeVVwZGF0ZWQnLFxuICAgICAgICBhY3Rpb246IGFjdGlvblxuICAgICAgfSk7XG4gICAgfSk7XG4gIH07XG5cbiAgX3Byb3RvLmdldERlZmF1bHRTdGF0ZSA9IGZ1bmN0aW9uIGdldERlZmF1bHRTdGF0ZShvcHRpb25zKSB7XG4gICAgdmFyIGRhdGEgPSB0eXBlb2Ygb3B0aW9ucy5pbml0aWFsRGF0YSA9PT0gJ2Z1bmN0aW9uJyA/IG9wdGlvbnMuaW5pdGlhbERhdGEoKSA6IG9wdGlvbnMuaW5pdGlhbERhdGE7XG4gICAgdmFyIGhhc0luaXRpYWxEYXRhID0gdHlwZW9mIG9wdGlvbnMuaW5pdGlhbERhdGEgIT09ICd1bmRlZmluZWQnO1xuICAgIHZhciBpbml0aWFsRGF0YVVwZGF0ZWRBdCA9IGhhc0luaXRpYWxEYXRhID8gdHlwZW9mIG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQgPT09ICdmdW5jdGlvbicgPyBvcHRpb25zLmluaXRpYWxEYXRhVXBkYXRlZEF0KCkgOiBvcHRpb25zLmluaXRpYWxEYXRhVXBkYXRlZEF0IDogMDtcbiAgICB2YXIgaGFzRGF0YSA9IHR5cGVvZiBkYXRhICE9PSAndW5kZWZpbmVkJztcbiAgICByZXR1cm4ge1xuICAgICAgZGF0YTogZGF0YSxcbiAgICAgIGRhdGFVcGRhdGVDb3VudDogMCxcbiAgICAgIGRhdGFVcGRhdGVkQXQ6IGhhc0RhdGEgPyBpbml0aWFsRGF0YVVwZGF0ZWRBdCAhPSBudWxsID8gaW5pdGlhbERhdGFVcGRhdGVkQXQgOiBEYXRlLm5vdygpIDogMCxcbiAgICAgIGVycm9yOiBudWxsLFxuICAgICAgZXJyb3JVcGRhdGVDb3VudDogMCxcbiAgICAgIGVycm9yVXBkYXRlZEF0OiAwLFxuICAgICAgZmV0Y2hGYWlsdXJlQ291bnQ6IDAsXG4gICAgICBmZXRjaE1ldGE6IG51bGwsXG4gICAgICBpc0ZldGNoaW5nOiBmYWxzZSxcbiAgICAgIGlzSW52YWxpZGF0ZWQ6IGZhbHNlLFxuICAgICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgICAgc3RhdHVzOiBoYXNEYXRhID8gJ3N1Y2Nlc3MnIDogJ2lkbGUnXG4gICAgfTtcbiAgfTtcblxuICBfcHJvdG8ucmVkdWNlciA9IGZ1bmN0aW9uIHJlZHVjZXIoc3RhdGUsIGFjdGlvbikge1xuICAgIHZhciBfYWN0aW9uJG1ldGEsIF9hY3Rpb24kZGF0YVVwZGF0ZWRBdDtcblxuICAgIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICAgIGNhc2UgJ2ZhaWxlZCc6XG4gICAgICAgIHJldHVybiBfZXh0ZW5kcyh7fSwgc3RhdGUsIHtcbiAgICAgICAgICBmZXRjaEZhaWx1cmVDb3VudDogc3RhdGUuZmV0Y2hGYWlsdXJlQ291bnQgKyAxXG4gICAgICAgIH0pO1xuXG4gICAgICBjYXNlICdwYXVzZSc6XG4gICAgICAgIHJldHVybiBfZXh0ZW5kcyh7fSwgc3RhdGUsIHtcbiAgICAgICAgICBpc1BhdXNlZDogdHJ1ZVxuICAgICAgICB9KTtcblxuICAgICAgY2FzZSAnY29udGludWUnOlxuICAgICAgICByZXR1cm4gX2V4dGVuZHMoe30sIHN0YXRlLCB7XG4gICAgICAgICAgaXNQYXVzZWQ6IGZhbHNlXG4gICAgICAgIH0pO1xuXG4gICAgICBjYXNlICdmZXRjaCc6XG4gICAgICAgIHJldHVybiBfZXh0ZW5kcyh7fSwgc3RhdGUsIHtcbiAgICAgICAgICBmZXRjaEZhaWx1cmVDb3VudDogMCxcbiAgICAgICAgICBmZXRjaE1ldGE6IChfYWN0aW9uJG1ldGEgPSBhY3Rpb24ubWV0YSkgIT0gbnVsbCA/IF9hY3Rpb24kbWV0YSA6IG51bGwsXG4gICAgICAgICAgaXNGZXRjaGluZzogdHJ1ZSxcbiAgICAgICAgICBpc1BhdXNlZDogZmFsc2VcbiAgICAgICAgfSwgIXN0YXRlLmRhdGFVcGRhdGVkQXQgJiYge1xuICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgIHN0YXR1czogJ2xvYWRpbmcnXG4gICAgICAgIH0pO1xuXG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuIF9leHRlbmRzKHt9LCBzdGF0ZSwge1xuICAgICAgICAgIGRhdGE6IGFjdGlvbi5kYXRhLFxuICAgICAgICAgIGRhdGFVcGRhdGVDb3VudDogc3RhdGUuZGF0YVVwZGF0ZUNvdW50ICsgMSxcbiAgICAgICAgICBkYXRhVXBkYXRlZEF0OiAoX2FjdGlvbiRkYXRhVXBkYXRlZEF0ID0gYWN0aW9uLmRhdGFVcGRhdGVkQXQpICE9IG51bGwgPyBfYWN0aW9uJGRhdGFVcGRhdGVkQXQgOiBEYXRlLm5vdygpLFxuICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiAwLFxuICAgICAgICAgIGlzRmV0Y2hpbmc6IGZhbHNlLFxuICAgICAgICAgIGlzSW52YWxpZGF0ZWQ6IGZhbHNlLFxuICAgICAgICAgIGlzUGF1c2VkOiBmYWxzZSxcbiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJ1xuICAgICAgICB9KTtcblxuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICB2YXIgZXJyb3IgPSBhY3Rpb24uZXJyb3I7XG5cbiAgICAgICAgaWYgKGlzQ2FuY2VsbGVkRXJyb3IoZXJyb3IpICYmIGVycm9yLnJldmVydCAmJiB0aGlzLnJldmVydFN0YXRlKSB7XG4gICAgICAgICAgcmV0dXJuIF9leHRlbmRzKHt9LCB0aGlzLnJldmVydFN0YXRlKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBfZXh0ZW5kcyh7fSwgc3RhdGUsIHtcbiAgICAgICAgICBlcnJvcjogZXJyb3IsXG4gICAgICAgICAgZXJyb3JVcGRhdGVDb3VudDogc3RhdGUuZXJyb3JVcGRhdGVDb3VudCArIDEsXG4gICAgICAgICAgZXJyb3JVcGRhdGVkQXQ6IERhdGUubm93KCksXG4gICAgICAgICAgZmV0Y2hGYWlsdXJlQ291bnQ6IHN0YXRlLmZldGNoRmFpbHVyZUNvdW50ICsgMSxcbiAgICAgICAgICBpc0ZldGNoaW5nOiBmYWxzZSxcbiAgICAgICAgICBpc1BhdXNlZDogZmFsc2UsXG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InXG4gICAgICAgIH0pO1xuXG4gICAgICBjYXNlICdpbnZhbGlkYXRlJzpcbiAgICAgICAgcmV0dXJuIF9leHRlbmRzKHt9LCBzdGF0ZSwge1xuICAgICAgICAgIGlzSW52YWxpZGF0ZWQ6IHRydWVcbiAgICAgICAgfSk7XG5cbiAgICAgIGNhc2UgJ3NldFN0YXRlJzpcbiAgICAgICAgcmV0dXJuIF9leHRlbmRzKHt9LCBzdGF0ZSwgYWN0aW9uLnN0YXRlKTtcblxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIHN0YXRlO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gUXVlcnk7XG59KCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryCache.js":
/*!********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryCache.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query */ \"(ssr)/./node_modules/react-query/es/core/query.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\n// CLASS\nvar QueryCache = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(QueryCache, _Subscribable);\n\n  function QueryCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n\n  var _proto = QueryCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : (0,_utils__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    var query = this.get(queryHash);\n\n    if (!query) {\n      query = new _query__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n\n    return query;\n  };\n\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(function (query) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);\n    });\n  };\n\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);\n    }) : this.queries;\n  };\n\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n\n  return QueryCache;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryClient.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryClient.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ \"(ssr)/./node_modules/react-query/es/core/queryCache.js\");\n/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutationCache */ \"(ssr)/./node_modules/react-query/es/core/mutationCache.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infiniteQueryBehavior */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\");\n\n\n\n\n\n\n\n\n// CLASS\nvar QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    this.queryCache = config.queryCache || new _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache();\n    this.mutationCache = config.mutationCache || new _mutationCache__WEBPACK_IMPORTED_MODULE_2__.MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n\n  var _proto = QueryClient.prototype;\n\n  _proto.mount = function mount() {\n    var _this = this;\n\n    this.unsubscribeFocus = _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.subscribe(function () {\n      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.subscribe(function () {\n      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      fetching: true\n    })).length;\n  };\n\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n          state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    var queryCache = this.queryCache;\n    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n\n    var _parseFilterArgs3 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs3[0],\n        options = _parseFilterArgs3[1];\n\n    var queryCache = this.queryCache;\n\n    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      active: true\n    });\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n\n    var _parseFilterArgs4 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs4[0],\n        _parseFilterArgs4$ = _parseFilterArgs4[1],\n        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n        _filters$refetchActiv,\n        _filters$refetchInact,\n        _this5 = this;\n\n    var _parseFilterArgs5 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs5[0],\n        options = _parseFilterArgs5[1];\n\n    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n\n    var _parseFilterArgs6 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs6[0],\n        options = _parseFilterArgs6[1];\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n    }\n\n    return promise;\n  };\n\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    parsedOptions.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__.infiniteQueryBehavior)();\n    return this.fetchQuery(parsedOptions);\n  };\n\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(queryKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.queryKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(mutationKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.mutationKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    var defaultedOptions = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n    }\n\n    return defaultedOptions;\n  };\n\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n\n  return QueryClient;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9xdWVyeUNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDNEQ7QUFDNUU7QUFDTTtBQUNGO0FBQ0U7QUFDQTtBQUNnQjtBQUNoRTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsK0NBQStDLG1EQUFVO0FBQ3pELHFEQUFxRCx5REFBYTtBQUNsRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBLDRCQUE0Qix1REFBWTtBQUN4QyxVQUFVLHVEQUFZLGdCQUFnQix5REFBYTtBQUNuRDs7QUFFQTtBQUNBO0FBQ0EsS0FBSztBQUNMLDZCQUE2Qix5REFBYTtBQUMxQyxVQUFVLHVEQUFZLGdCQUFnQix5REFBYTtBQUNuRDs7QUFFQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkJBQTJCLHVEQUFlO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHNDQUFzQyw4RUFBUSxHQUFHO0FBQ2pEO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQSx3QkFBd0Isc0RBQWM7QUFDdEM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsV0FBVyx5REFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLDRCQUE0Qix1REFBZTtBQUMzQzs7QUFFQTtBQUNBLElBQUkseURBQWE7QUFDakI7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7O0FBRUE7QUFDQTs7QUFFQSw0QkFBNEIsdURBQWU7QUFDM0M7QUFDQTs7QUFFQTs7QUFFQSx5QkFBeUIsOEVBQVEsR0FBRztBQUNwQztBQUNBLEtBQUs7O0FBRUwsV0FBVyx5REFBYTtBQUN4QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7O0FBRUEsNEJBQTRCLHVEQUFlO0FBQzNDO0FBQ0E7QUFDQSwyREFBMkQ7O0FBRTNEO0FBQ0E7QUFDQTs7QUFFQSxtQkFBbUIseURBQWE7QUFDaEM7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0wsc0NBQXNDLHdDQUFJLFFBQVEsd0NBQUk7QUFDdEQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0QkFBNEIsdURBQWU7QUFDM0M7QUFDQTs7QUFFQSx5QkFBeUIsOEVBQVEsR0FBRztBQUNwQztBQUNBLGtDQUFrQyxlQUFlO0FBQ2pEO0FBQ0E7QUFDQSxLQUFLOztBQUVMLFdBQVcseURBQWE7QUFDeEI7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTs7QUFFQSw0QkFBNEIsdURBQWU7QUFDM0M7QUFDQTs7QUFFQSxtQkFBbUIseURBQWE7QUFDaEM7QUFDQSxzQ0FBc0MsOEVBQVEsR0FBRztBQUNqRDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQLEtBQUs7QUFDTCw2Q0FBNkMsd0NBQUk7O0FBRWpEO0FBQ0EsOEJBQThCLHdDQUFJO0FBQ2xDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSx3QkFBd0Isc0RBQWM7QUFDdEMsb0VBQW9FOztBQUVwRTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0RBQWtELHdDQUFJLFFBQVEsd0NBQUk7QUFDbEU7O0FBRUE7QUFDQSx3QkFBd0Isc0RBQWM7QUFDdEMsNkJBQTZCLDZFQUFxQjtBQUNsRDtBQUNBOztBQUVBO0FBQ0EsMERBQTBELHdDQUFJLFFBQVEsd0NBQUk7QUFDMUU7O0FBRUE7QUFDQTs7QUFFQSxtQkFBbUIseURBQWE7QUFDaEM7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0wsc0NBQXNDLHdDQUFJLFFBQVEsd0NBQUk7QUFDdEQ7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsb0RBQVksZUFBZSxvREFBWTtBQUNwRCxLQUFLOztBQUVMO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLGFBQWEsdURBQWU7QUFDNUIsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQSxhQUFhLG9EQUFZLGtCQUFrQixvREFBWTtBQUN2RCxLQUFLOztBQUVMO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLGFBQWEsdURBQWU7QUFDNUIsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDJCQUEyQiw4RUFBUSxHQUFHO0FBQ3RDO0FBQ0EsS0FBSzs7QUFFTDtBQUNBLG1DQUFtQyw2REFBcUI7QUFDeEQ7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyw4RUFBUSxHQUFHO0FBQ3RCO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL2NvcmUvcXVlcnlDbGllbnQuanM/NmY5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCB7IGhhc2hRdWVyeUtleSwgbm9vcCwgcGFyc2VGaWx0ZXJBcmdzLCBwYXJzZVF1ZXJ5QXJncywgcGFydGlhbE1hdGNoS2V5LCBoYXNoUXVlcnlLZXlCeU9wdGlvbnMgfSBmcm9tICcuL3V0aWxzJztcbmltcG9ydCB7IFF1ZXJ5Q2FjaGUgfSBmcm9tICcuL3F1ZXJ5Q2FjaGUnO1xuaW1wb3J0IHsgTXV0YXRpb25DYWNoZSB9IGZyb20gJy4vbXV0YXRpb25DYWNoZSc7XG5pbXBvcnQgeyBmb2N1c01hbmFnZXIgfSBmcm9tICcuL2ZvY3VzTWFuYWdlcic7XG5pbXBvcnQgeyBvbmxpbmVNYW5hZ2VyIH0gZnJvbSAnLi9vbmxpbmVNYW5hZ2VyJztcbmltcG9ydCB7IG5vdGlmeU1hbmFnZXIgfSBmcm9tICcuL25vdGlmeU1hbmFnZXInO1xuaW1wb3J0IHsgaW5maW5pdGVRdWVyeUJlaGF2aW9yIH0gZnJvbSAnLi9pbmZpbml0ZVF1ZXJ5QmVoYXZpb3InO1xuLy8gQ0xBU1NcbmV4cG9ydCB2YXIgUXVlcnlDbGllbnQgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICBmdW5jdGlvbiBRdWVyeUNsaWVudChjb25maWcpIHtcbiAgICBpZiAoY29uZmlnID09PSB2b2lkIDApIHtcbiAgICAgIGNvbmZpZyA9IHt9O1xuICAgIH1cblxuICAgIHRoaXMucXVlcnlDYWNoZSA9IGNvbmZpZy5xdWVyeUNhY2hlIHx8IG5ldyBRdWVyeUNhY2hlKCk7XG4gICAgdGhpcy5tdXRhdGlvbkNhY2hlID0gY29uZmlnLm11dGF0aW9uQ2FjaGUgfHwgbmV3IE11dGF0aW9uQ2FjaGUoKTtcbiAgICB0aGlzLmRlZmF1bHRPcHRpb25zID0gY29uZmlnLmRlZmF1bHRPcHRpb25zIHx8IHt9O1xuICAgIHRoaXMucXVlcnlEZWZhdWx0cyA9IFtdO1xuICAgIHRoaXMubXV0YXRpb25EZWZhdWx0cyA9IFtdO1xuICB9XG5cbiAgdmFyIF9wcm90byA9IFF1ZXJ5Q2xpZW50LnByb3RvdHlwZTtcblxuICBfcHJvdG8ubW91bnQgPSBmdW5jdGlvbiBtb3VudCgpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgdGhpcy51bnN1YnNjcmliZUZvY3VzID0gZm9jdXNNYW5hZ2VyLnN1YnNjcmliZShmdW5jdGlvbiAoKSB7XG4gICAgICBpZiAoZm9jdXNNYW5hZ2VyLmlzRm9jdXNlZCgpICYmIG9ubGluZU1hbmFnZXIuaXNPbmxpbmUoKSkge1xuICAgICAgICBfdGhpcy5tdXRhdGlvbkNhY2hlLm9uRm9jdXMoKTtcblxuICAgICAgICBfdGhpcy5xdWVyeUNhY2hlLm9uRm9jdXMoKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICB0aGlzLnVuc3Vic2NyaWJlT25saW5lID0gb25saW5lTWFuYWdlci5zdWJzY3JpYmUoZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKGZvY3VzTWFuYWdlci5pc0ZvY3VzZWQoKSAmJiBvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCkpIHtcbiAgICAgICAgX3RoaXMubXV0YXRpb25DYWNoZS5vbk9ubGluZSgpO1xuXG4gICAgICAgIF90aGlzLnF1ZXJ5Q2FjaGUub25PbmxpbmUoKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfTtcblxuICBfcHJvdG8udW5tb3VudCA9IGZ1bmN0aW9uIHVubW91bnQoKSB7XG4gICAgdmFyIF90aGlzJHVuc3Vic2NyaWJlRm9jdSwgX3RoaXMkdW5zdWJzY3JpYmVPbmxpO1xuXG4gICAgKF90aGlzJHVuc3Vic2NyaWJlRm9jdSA9IHRoaXMudW5zdWJzY3JpYmVGb2N1cykgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJHVuc3Vic2NyaWJlRm9jdS5jYWxsKHRoaXMpO1xuICAgIChfdGhpcyR1bnN1YnNjcmliZU9ubGkgPSB0aGlzLnVuc3Vic2NyaWJlT25saW5lKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMkdW5zdWJzY3JpYmVPbmxpLmNhbGwodGhpcyk7XG4gIH07XG5cbiAgX3Byb3RvLmlzRmV0Y2hpbmcgPSBmdW5jdGlvbiBpc0ZldGNoaW5nKGFyZzEsIGFyZzIpIHtcbiAgICB2YXIgX3BhcnNlRmlsdGVyQXJncyA9IHBhcnNlRmlsdGVyQXJncyhhcmcxLCBhcmcyKSxcbiAgICAgICAgZmlsdGVycyA9IF9wYXJzZUZpbHRlckFyZ3NbMF07XG5cbiAgICBmaWx0ZXJzLmZldGNoaW5nID0gdHJ1ZTtcbiAgICByZXR1cm4gdGhpcy5xdWVyeUNhY2hlLmZpbmRBbGwoZmlsdGVycykubGVuZ3RoO1xuICB9O1xuXG4gIF9wcm90by5pc011dGF0aW5nID0gZnVuY3Rpb24gaXNNdXRhdGluZyhmaWx0ZXJzKSB7XG4gICAgcmV0dXJuIHRoaXMubXV0YXRpb25DYWNoZS5maW5kQWxsKF9leHRlbmRzKHt9LCBmaWx0ZXJzLCB7XG4gICAgICBmZXRjaGluZzogdHJ1ZVxuICAgIH0pKS5sZW5ndGg7XG4gIH07XG5cbiAgX3Byb3RvLmdldFF1ZXJ5RGF0YSA9IGZ1bmN0aW9uIGdldFF1ZXJ5RGF0YShxdWVyeUtleSwgZmlsdGVycykge1xuICAgIHZhciBfdGhpcyRxdWVyeUNhY2hlJGZpbmQ7XG5cbiAgICByZXR1cm4gKF90aGlzJHF1ZXJ5Q2FjaGUkZmluZCA9IHRoaXMucXVlcnlDYWNoZS5maW5kKHF1ZXJ5S2V5LCBmaWx0ZXJzKSkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJHF1ZXJ5Q2FjaGUkZmluZC5zdGF0ZS5kYXRhO1xuICB9O1xuXG4gIF9wcm90by5nZXRRdWVyaWVzRGF0YSA9IGZ1bmN0aW9uIGdldFF1ZXJpZXNEYXRhKHF1ZXJ5S2V5T3JGaWx0ZXJzKSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0UXVlcnlDYWNoZSgpLmZpbmRBbGwocXVlcnlLZXlPckZpbHRlcnMpLm1hcChmdW5jdGlvbiAoX3JlZikge1xuICAgICAgdmFyIHF1ZXJ5S2V5ID0gX3JlZi5xdWVyeUtleSxcbiAgICAgICAgICBzdGF0ZSA9IF9yZWYuc3RhdGU7XG4gICAgICB2YXIgZGF0YSA9IHN0YXRlLmRhdGE7XG4gICAgICByZXR1cm4gW3F1ZXJ5S2V5LCBkYXRhXTtcbiAgICB9KTtcbiAgfTtcblxuICBfcHJvdG8uc2V0UXVlcnlEYXRhID0gZnVuY3Rpb24gc2V0UXVlcnlEYXRhKHF1ZXJ5S2V5LCB1cGRhdGVyLCBvcHRpb25zKSB7XG4gICAgdmFyIHBhcnNlZE9wdGlvbnMgPSBwYXJzZVF1ZXJ5QXJncyhxdWVyeUtleSk7XG4gICAgdmFyIGRlZmF1bHRlZE9wdGlvbnMgPSB0aGlzLmRlZmF1bHRRdWVyeU9wdGlvbnMocGFyc2VkT3B0aW9ucyk7XG4gICAgcmV0dXJuIHRoaXMucXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKS5zZXREYXRhKHVwZGF0ZXIsIG9wdGlvbnMpO1xuICB9O1xuXG4gIF9wcm90by5zZXRRdWVyaWVzRGF0YSA9IGZ1bmN0aW9uIHNldFF1ZXJpZXNEYXRhKHF1ZXJ5S2V5T3JGaWx0ZXJzLCB1cGRhdGVyLCBvcHRpb25zKSB7XG4gICAgdmFyIF90aGlzMiA9IHRoaXM7XG5cbiAgICByZXR1cm4gbm90aWZ5TWFuYWdlci5iYXRjaChmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gX3RoaXMyLmdldFF1ZXJ5Q2FjaGUoKS5maW5kQWxsKHF1ZXJ5S2V5T3JGaWx0ZXJzKS5tYXAoZnVuY3Rpb24gKF9yZWYyKSB7XG4gICAgICAgIHZhciBxdWVyeUtleSA9IF9yZWYyLnF1ZXJ5S2V5O1xuICAgICAgICByZXR1cm4gW3F1ZXJ5S2V5LCBfdGhpczIuc2V0UXVlcnlEYXRhKHF1ZXJ5S2V5LCB1cGRhdGVyLCBvcHRpb25zKV07XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfTtcblxuICBfcHJvdG8uZ2V0UXVlcnlTdGF0ZSA9IGZ1bmN0aW9uIGdldFF1ZXJ5U3RhdGUocXVlcnlLZXksIGZpbHRlcnMpIHtcbiAgICB2YXIgX3RoaXMkcXVlcnlDYWNoZSRmaW5kMjtcblxuICAgIHJldHVybiAoX3RoaXMkcXVlcnlDYWNoZSRmaW5kMiA9IHRoaXMucXVlcnlDYWNoZS5maW5kKHF1ZXJ5S2V5LCBmaWx0ZXJzKSkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJHF1ZXJ5Q2FjaGUkZmluZDIuc3RhdGU7XG4gIH07XG5cbiAgX3Byb3RvLnJlbW92ZVF1ZXJpZXMgPSBmdW5jdGlvbiByZW1vdmVRdWVyaWVzKGFyZzEsIGFyZzIpIHtcbiAgICB2YXIgX3BhcnNlRmlsdGVyQXJnczIgPSBwYXJzZUZpbHRlckFyZ3MoYXJnMSwgYXJnMiksXG4gICAgICAgIGZpbHRlcnMgPSBfcGFyc2VGaWx0ZXJBcmdzMlswXTtcblxuICAgIHZhciBxdWVyeUNhY2hlID0gdGhpcy5xdWVyeUNhY2hlO1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goZnVuY3Rpb24gKCkge1xuICAgICAgcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goZnVuY3Rpb24gKHF1ZXJ5KSB7XG4gICAgICAgIHF1ZXJ5Q2FjaGUucmVtb3ZlKHF1ZXJ5KTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9O1xuXG4gIF9wcm90by5yZXNldFF1ZXJpZXMgPSBmdW5jdGlvbiByZXNldFF1ZXJpZXMoYXJnMSwgYXJnMiwgYXJnMykge1xuICAgIHZhciBfdGhpczMgPSB0aGlzO1xuXG4gICAgdmFyIF9wYXJzZUZpbHRlckFyZ3MzID0gcGFyc2VGaWx0ZXJBcmdzKGFyZzEsIGFyZzIsIGFyZzMpLFxuICAgICAgICBmaWx0ZXJzID0gX3BhcnNlRmlsdGVyQXJnczNbMF0sXG4gICAgICAgIG9wdGlvbnMgPSBfcGFyc2VGaWx0ZXJBcmdzM1sxXTtcblxuICAgIHZhciBxdWVyeUNhY2hlID0gdGhpcy5xdWVyeUNhY2hlO1xuXG4gICAgdmFyIHJlZmV0Y2hGaWx0ZXJzID0gX2V4dGVuZHMoe30sIGZpbHRlcnMsIHtcbiAgICAgIGFjdGl2ZTogdHJ1ZVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIG5vdGlmeU1hbmFnZXIuYmF0Y2goZnVuY3Rpb24gKCkge1xuICAgICAgcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goZnVuY3Rpb24gKHF1ZXJ5KSB7XG4gICAgICAgIHF1ZXJ5LnJlc2V0KCk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBfdGhpczMucmVmZXRjaFF1ZXJpZXMocmVmZXRjaEZpbHRlcnMsIG9wdGlvbnMpO1xuICAgIH0pO1xuICB9O1xuXG4gIF9wcm90by5jYW5jZWxRdWVyaWVzID0gZnVuY3Rpb24gY2FuY2VsUXVlcmllcyhhcmcxLCBhcmcyLCBhcmczKSB7XG4gICAgdmFyIF90aGlzNCA9IHRoaXM7XG5cbiAgICB2YXIgX3BhcnNlRmlsdGVyQXJnczQgPSBwYXJzZUZpbHRlckFyZ3MoYXJnMSwgYXJnMiwgYXJnMyksXG4gICAgICAgIGZpbHRlcnMgPSBfcGFyc2VGaWx0ZXJBcmdzNFswXSxcbiAgICAgICAgX3BhcnNlRmlsdGVyQXJnczQkID0gX3BhcnNlRmlsdGVyQXJnczRbMV0sXG4gICAgICAgIGNhbmNlbE9wdGlvbnMgPSBfcGFyc2VGaWx0ZXJBcmdzNCQgPT09IHZvaWQgMCA/IHt9IDogX3BhcnNlRmlsdGVyQXJnczQkO1xuXG4gICAgaWYgKHR5cGVvZiBjYW5jZWxPcHRpb25zLnJldmVydCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNhbmNlbE9wdGlvbnMucmV2ZXJ0ID0gdHJ1ZTtcbiAgICB9XG5cbiAgICB2YXIgcHJvbWlzZXMgPSBub3RpZnlNYW5hZ2VyLmJhdGNoKGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBfdGhpczQucXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLm1hcChmdW5jdGlvbiAocXVlcnkpIHtcbiAgICAgICAgcmV0dXJuIHF1ZXJ5LmNhbmNlbChjYW5jZWxPcHRpb25zKTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIHJldHVybiBQcm9taXNlLmFsbChwcm9taXNlcykudGhlbihub29wKS5jYXRjaChub29wKTtcbiAgfTtcblxuICBfcHJvdG8uaW52YWxpZGF0ZVF1ZXJpZXMgPSBmdW5jdGlvbiBpbnZhbGlkYXRlUXVlcmllcyhhcmcxLCBhcmcyLCBhcmczKSB7XG4gICAgdmFyIF9yZWYzLFxuICAgICAgICBfZmlsdGVycyRyZWZldGNoQWN0aXYsXG4gICAgICAgIF9maWx0ZXJzJHJlZmV0Y2hJbmFjdCxcbiAgICAgICAgX3RoaXM1ID0gdGhpcztcblxuICAgIHZhciBfcGFyc2VGaWx0ZXJBcmdzNSA9IHBhcnNlRmlsdGVyQXJncyhhcmcxLCBhcmcyLCBhcmczKSxcbiAgICAgICAgZmlsdGVycyA9IF9wYXJzZUZpbHRlckFyZ3M1WzBdLFxuICAgICAgICBvcHRpb25zID0gX3BhcnNlRmlsdGVyQXJnczVbMV07XG5cbiAgICB2YXIgcmVmZXRjaEZpbHRlcnMgPSBfZXh0ZW5kcyh7fSwgZmlsdGVycywge1xuICAgICAgLy8gaWYgZmlsdGVycy5yZWZldGNoQWN0aXZlIGlzIG5vdCBwcm92aWRlZCBhbmQgZmlsdGVycy5hY3RpdmUgaXMgZXhwbGljaXRseSBmYWxzZSxcbiAgICAgIC8vIGUuZy4gaW52YWxpZGF0ZVF1ZXJpZXMoeyBhY3RpdmU6IGZhbHNlIH0pLCB3ZSBkb24ndCB3YW50IHRvIHJlZmV0Y2ggYWN0aXZlIHF1ZXJpZXNcbiAgICAgIGFjdGl2ZTogKF9yZWYzID0gKF9maWx0ZXJzJHJlZmV0Y2hBY3RpdiA9IGZpbHRlcnMucmVmZXRjaEFjdGl2ZSkgIT0gbnVsbCA/IF9maWx0ZXJzJHJlZmV0Y2hBY3RpdiA6IGZpbHRlcnMuYWN0aXZlKSAhPSBudWxsID8gX3JlZjMgOiB0cnVlLFxuICAgICAgaW5hY3RpdmU6IChfZmlsdGVycyRyZWZldGNoSW5hY3QgPSBmaWx0ZXJzLnJlZmV0Y2hJbmFjdGl2ZSkgIT0gbnVsbCA/IF9maWx0ZXJzJHJlZmV0Y2hJbmFjdCA6IGZhbHNlXG4gICAgfSk7XG5cbiAgICByZXR1cm4gbm90aWZ5TWFuYWdlci5iYXRjaChmdW5jdGlvbiAoKSB7XG4gICAgICBfdGhpczUucXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goZnVuY3Rpb24gKHF1ZXJ5KSB7XG4gICAgICAgIHF1ZXJ5LmludmFsaWRhdGUoKTtcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gX3RoaXM1LnJlZmV0Y2hRdWVyaWVzKHJlZmV0Y2hGaWx0ZXJzLCBvcHRpb25zKTtcbiAgICB9KTtcbiAgfTtcblxuICBfcHJvdG8ucmVmZXRjaFF1ZXJpZXMgPSBmdW5jdGlvbiByZWZldGNoUXVlcmllcyhhcmcxLCBhcmcyLCBhcmczKSB7XG4gICAgdmFyIF90aGlzNiA9IHRoaXM7XG5cbiAgICB2YXIgX3BhcnNlRmlsdGVyQXJnczYgPSBwYXJzZUZpbHRlckFyZ3MoYXJnMSwgYXJnMiwgYXJnMyksXG4gICAgICAgIGZpbHRlcnMgPSBfcGFyc2VGaWx0ZXJBcmdzNlswXSxcbiAgICAgICAgb3B0aW9ucyA9IF9wYXJzZUZpbHRlckFyZ3M2WzFdO1xuXG4gICAgdmFyIHByb21pc2VzID0gbm90aWZ5TWFuYWdlci5iYXRjaChmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gX3RoaXM2LnF1ZXJ5Q2FjaGUuZmluZEFsbChmaWx0ZXJzKS5tYXAoZnVuY3Rpb24gKHF1ZXJ5KSB7XG4gICAgICAgIHJldHVybiBxdWVyeS5mZXRjaCh1bmRlZmluZWQsIF9leHRlbmRzKHt9LCBvcHRpb25zLCB7XG4gICAgICAgICAgbWV0YToge1xuICAgICAgICAgICAgcmVmZXRjaFBhZ2U6IGZpbHRlcnMgPT0gbnVsbCA/IHZvaWQgMCA6IGZpbHRlcnMucmVmZXRjaFBhZ2VcbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIHZhciBwcm9taXNlID0gUHJvbWlzZS5hbGwocHJvbWlzZXMpLnRoZW4obm9vcCk7XG5cbiAgICBpZiAoIShvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnRocm93T25FcnJvcikpIHtcbiAgICAgIHByb21pc2UgPSBwcm9taXNlLmNhdGNoKG5vb3ApO1xuICAgIH1cblxuICAgIHJldHVybiBwcm9taXNlO1xuICB9O1xuXG4gIF9wcm90by5mZXRjaFF1ZXJ5ID0gZnVuY3Rpb24gZmV0Y2hRdWVyeShhcmcxLCBhcmcyLCBhcmczKSB7XG4gICAgdmFyIHBhcnNlZE9wdGlvbnMgPSBwYXJzZVF1ZXJ5QXJncyhhcmcxLCBhcmcyLCBhcmczKTtcbiAgICB2YXIgZGVmYXVsdGVkT3B0aW9ucyA9IHRoaXMuZGVmYXVsdFF1ZXJ5T3B0aW9ucyhwYXJzZWRPcHRpb25zKTsgLy8gaHR0cHM6Ly9naXRodWIuY29tL3Rhbm5lcmxpbnNsZXkvcmVhY3QtcXVlcnkvaXNzdWVzLzY1MlxuXG4gICAgaWYgKHR5cGVvZiBkZWZhdWx0ZWRPcHRpb25zLnJldHJ5ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5yZXRyeSA9IGZhbHNlO1xuICAgIH1cblxuICAgIHZhciBxdWVyeSA9IHRoaXMucXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKTtcbiAgICByZXR1cm4gcXVlcnkuaXNTdGFsZUJ5VGltZShkZWZhdWx0ZWRPcHRpb25zLnN0YWxlVGltZSkgPyBxdWVyeS5mZXRjaChkZWZhdWx0ZWRPcHRpb25zKSA6IFByb21pc2UucmVzb2x2ZShxdWVyeS5zdGF0ZS5kYXRhKTtcbiAgfTtcblxuICBfcHJvdG8ucHJlZmV0Y2hRdWVyeSA9IGZ1bmN0aW9uIHByZWZldGNoUXVlcnkoYXJnMSwgYXJnMiwgYXJnMykge1xuICAgIHJldHVybiB0aGlzLmZldGNoUXVlcnkoYXJnMSwgYXJnMiwgYXJnMykudGhlbihub29wKS5jYXRjaChub29wKTtcbiAgfTtcblxuICBfcHJvdG8uZmV0Y2hJbmZpbml0ZVF1ZXJ5ID0gZnVuY3Rpb24gZmV0Y2hJbmZpbml0ZVF1ZXJ5KGFyZzEsIGFyZzIsIGFyZzMpIHtcbiAgICB2YXIgcGFyc2VkT3B0aW9ucyA9IHBhcnNlUXVlcnlBcmdzKGFyZzEsIGFyZzIsIGFyZzMpO1xuICAgIHBhcnNlZE9wdGlvbnMuYmVoYXZpb3IgPSBpbmZpbml0ZVF1ZXJ5QmVoYXZpb3IoKTtcbiAgICByZXR1cm4gdGhpcy5mZXRjaFF1ZXJ5KHBhcnNlZE9wdGlvbnMpO1xuICB9O1xuXG4gIF9wcm90by5wcmVmZXRjaEluZmluaXRlUXVlcnkgPSBmdW5jdGlvbiBwcmVmZXRjaEluZmluaXRlUXVlcnkoYXJnMSwgYXJnMiwgYXJnMykge1xuICAgIHJldHVybiB0aGlzLmZldGNoSW5maW5pdGVRdWVyeShhcmcxLCBhcmcyLCBhcmczKS50aGVuKG5vb3ApLmNhdGNoKG5vb3ApO1xuICB9O1xuXG4gIF9wcm90by5jYW5jZWxNdXRhdGlvbnMgPSBmdW5jdGlvbiBjYW5jZWxNdXRhdGlvbnMoKSB7XG4gICAgdmFyIF90aGlzNyA9IHRoaXM7XG5cbiAgICB2YXIgcHJvbWlzZXMgPSBub3RpZnlNYW5hZ2VyLmJhdGNoKGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBfdGhpczcubXV0YXRpb25DYWNoZS5nZXRBbGwoKS5tYXAoZnVuY3Rpb24gKG11dGF0aW9uKSB7XG4gICAgICAgIHJldHVybiBtdXRhdGlvbi5jYW5jZWwoKTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIHJldHVybiBQcm9taXNlLmFsbChwcm9taXNlcykudGhlbihub29wKS5jYXRjaChub29wKTtcbiAgfTtcblxuICBfcHJvdG8ucmVzdW1lUGF1c2VkTXV0YXRpb25zID0gZnVuY3Rpb24gcmVzdW1lUGF1c2VkTXV0YXRpb25zKCkge1xuICAgIHJldHVybiB0aGlzLmdldE11dGF0aW9uQ2FjaGUoKS5yZXN1bWVQYXVzZWRNdXRhdGlvbnMoKTtcbiAgfTtcblxuICBfcHJvdG8uZXhlY3V0ZU11dGF0aW9uID0gZnVuY3Rpb24gZXhlY3V0ZU11dGF0aW9uKG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5tdXRhdGlvbkNhY2hlLmJ1aWxkKHRoaXMsIG9wdGlvbnMpLmV4ZWN1dGUoKTtcbiAgfTtcblxuICBfcHJvdG8uZ2V0UXVlcnlDYWNoZSA9IGZ1bmN0aW9uIGdldFF1ZXJ5Q2FjaGUoKSB7XG4gICAgcmV0dXJuIHRoaXMucXVlcnlDYWNoZTtcbiAgfTtcblxuICBfcHJvdG8uZ2V0TXV0YXRpb25DYWNoZSA9IGZ1bmN0aW9uIGdldE11dGF0aW9uQ2FjaGUoKSB7XG4gICAgcmV0dXJuIHRoaXMubXV0YXRpb25DYWNoZTtcbiAgfTtcblxuICBfcHJvdG8uZ2V0RGVmYXVsdE9wdGlvbnMgPSBmdW5jdGlvbiBnZXREZWZhdWx0T3B0aW9ucygpIHtcbiAgICByZXR1cm4gdGhpcy5kZWZhdWx0T3B0aW9ucztcbiAgfTtcblxuICBfcHJvdG8uc2V0RGVmYXVsdE9wdGlvbnMgPSBmdW5jdGlvbiBzZXREZWZhdWx0T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgdGhpcy5kZWZhdWx0T3B0aW9ucyA9IG9wdGlvbnM7XG4gIH07XG5cbiAgX3Byb3RvLnNldFF1ZXJ5RGVmYXVsdHMgPSBmdW5jdGlvbiBzZXRRdWVyeURlZmF1bHRzKHF1ZXJ5S2V5LCBvcHRpb25zKSB7XG4gICAgdmFyIHJlc3VsdCA9IHRoaXMucXVlcnlEZWZhdWx0cy5maW5kKGZ1bmN0aW9uICh4KSB7XG4gICAgICByZXR1cm4gaGFzaFF1ZXJ5S2V5KHF1ZXJ5S2V5KSA9PT0gaGFzaFF1ZXJ5S2V5KHgucXVlcnlLZXkpO1xuICAgIH0pO1xuXG4gICAgaWYgKHJlc3VsdCkge1xuICAgICAgcmVzdWx0LmRlZmF1bHRPcHRpb25zID0gb3B0aW9ucztcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5xdWVyeURlZmF1bHRzLnB1c2goe1xuICAgICAgICBxdWVyeUtleTogcXVlcnlLZXksXG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiBvcHRpb25zXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgX3Byb3RvLmdldFF1ZXJ5RGVmYXVsdHMgPSBmdW5jdGlvbiBnZXRRdWVyeURlZmF1bHRzKHF1ZXJ5S2V5KSB7XG4gICAgdmFyIF90aGlzJHF1ZXJ5RGVmYXVsdHMkZjtcblxuICAgIHJldHVybiBxdWVyeUtleSA/IChfdGhpcyRxdWVyeURlZmF1bHRzJGYgPSB0aGlzLnF1ZXJ5RGVmYXVsdHMuZmluZChmdW5jdGlvbiAoeCkge1xuICAgICAgcmV0dXJuIHBhcnRpYWxNYXRjaEtleShxdWVyeUtleSwgeC5xdWVyeUtleSk7XG4gICAgfSkpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRxdWVyeURlZmF1bHRzJGYuZGVmYXVsdE9wdGlvbnMgOiB1bmRlZmluZWQ7XG4gIH07XG5cbiAgX3Byb3RvLnNldE11dGF0aW9uRGVmYXVsdHMgPSBmdW5jdGlvbiBzZXRNdXRhdGlvbkRlZmF1bHRzKG11dGF0aW9uS2V5LCBvcHRpb25zKSB7XG4gICAgdmFyIHJlc3VsdCA9IHRoaXMubXV0YXRpb25EZWZhdWx0cy5maW5kKGZ1bmN0aW9uICh4KSB7XG4gICAgICByZXR1cm4gaGFzaFF1ZXJ5S2V5KG11dGF0aW9uS2V5KSA9PT0gaGFzaFF1ZXJ5S2V5KHgubXV0YXRpb25LZXkpO1xuICAgIH0pO1xuXG4gICAgaWYgKHJlc3VsdCkge1xuICAgICAgcmVzdWx0LmRlZmF1bHRPcHRpb25zID0gb3B0aW9ucztcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5tdXRhdGlvbkRlZmF1bHRzLnB1c2goe1xuICAgICAgICBtdXRhdGlvbktleTogbXV0YXRpb25LZXksXG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiBvcHRpb25zXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgX3Byb3RvLmdldE11dGF0aW9uRGVmYXVsdHMgPSBmdW5jdGlvbiBnZXRNdXRhdGlvbkRlZmF1bHRzKG11dGF0aW9uS2V5KSB7XG4gICAgdmFyIF90aGlzJG11dGF0aW9uRGVmYXVsdDtcblxuICAgIHJldHVybiBtdXRhdGlvbktleSA/IChfdGhpcyRtdXRhdGlvbkRlZmF1bHQgPSB0aGlzLm11dGF0aW9uRGVmYXVsdHMuZmluZChmdW5jdGlvbiAoeCkge1xuICAgICAgcmV0dXJuIHBhcnRpYWxNYXRjaEtleShtdXRhdGlvbktleSwgeC5tdXRhdGlvbktleSk7XG4gICAgfSkpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRtdXRhdGlvbkRlZmF1bHQuZGVmYXVsdE9wdGlvbnMgOiB1bmRlZmluZWQ7XG4gIH07XG5cbiAgX3Byb3RvLmRlZmF1bHRRdWVyeU9wdGlvbnMgPSBmdW5jdGlvbiBkZWZhdWx0UXVlcnlPcHRpb25zKG9wdGlvbnMpIHtcbiAgICBpZiAob3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5fZGVmYXVsdGVkKSB7XG4gICAgICByZXR1cm4gb3B0aW9ucztcbiAgICB9XG5cbiAgICB2YXIgZGVmYXVsdGVkT3B0aW9ucyA9IF9leHRlbmRzKHt9LCB0aGlzLmRlZmF1bHRPcHRpb25zLnF1ZXJpZXMsIHRoaXMuZ2V0UXVlcnlEZWZhdWx0cyhvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnF1ZXJ5S2V5KSwgb3B0aW9ucywge1xuICAgICAgX2RlZmF1bHRlZDogdHJ1ZVxuICAgIH0pO1xuXG4gICAgaWYgKCFkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5SGFzaCAmJiBkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5S2V5KSB7XG4gICAgICBkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5SGFzaCA9IGhhc2hRdWVyeUtleUJ5T3B0aW9ucyhkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5S2V5LCBkZWZhdWx0ZWRPcHRpb25zKTtcbiAgICB9XG5cbiAgICByZXR1cm4gZGVmYXVsdGVkT3B0aW9ucztcbiAgfTtcblxuICBfcHJvdG8uZGVmYXVsdFF1ZXJ5T2JzZXJ2ZXJPcHRpb25zID0gZnVuY3Rpb24gZGVmYXVsdFF1ZXJ5T2JzZXJ2ZXJPcHRpb25zKG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5kZWZhdWx0UXVlcnlPcHRpb25zKG9wdGlvbnMpO1xuICB9O1xuXG4gIF9wcm90by5kZWZhdWx0TXV0YXRpb25PcHRpb25zID0gZnVuY3Rpb24gZGVmYXVsdE11dGF0aW9uT3B0aW9ucyhvcHRpb25zKSB7XG4gICAgaWYgKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuX2RlZmF1bHRlZCkge1xuICAgICAgcmV0dXJuIG9wdGlvbnM7XG4gICAgfVxuXG4gICAgcmV0dXJuIF9leHRlbmRzKHt9LCB0aGlzLmRlZmF1bHRPcHRpb25zLm11dGF0aW9ucywgdGhpcy5nZXRNdXRhdGlvbkRlZmF1bHRzKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMubXV0YXRpb25LZXkpLCBvcHRpb25zLCB7XG4gICAgICBfZGVmYXVsdGVkOiB0cnVlXG4gICAgfSk7XG4gIH07XG5cbiAgX3Byb3RvLmNsZWFyID0gZnVuY3Rpb24gY2xlYXIoKSB7XG4gICAgdGhpcy5xdWVyeUNhY2hlLmNsZWFyKCk7XG4gICAgdGhpcy5tdXRhdGlvbkNhY2hlLmNsZWFyKCk7XG4gIH07XG5cbiAgcmV0dXJuIFF1ZXJ5Q2xpZW50O1xufSgpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryObserver.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryObserver.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n\n\n\n\n\n\n\n\nvar QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(QueryObserver, _Subscribable);\n\n  function QueryObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n\n    _this.bindMethods();\n\n    _this.setOptions(options);\n\n    return _this;\n  }\n\n  var _proto = QueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n\n    var trackedResult = {};\n\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n\n    return trackedResult;\n  };\n\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n\n  _proto.refetch = function refetch(options) {\n    return this.fetch((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n\n      return _this5.currentResult;\n    });\n  };\n\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n    }\n\n    return promise;\n  };\n\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n\n    this.clearStaleTimeout();\n\n    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.currentResult.isStale || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.options.staleTime)) {\n      return;\n    }\n\n    var time = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.options.enabled === false || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n        error = state.error,\n        errorUpdatedAt = state.errorUpdatedAt,\n        isFetching = state.isFetching,\n        status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n        // Memoize select result\n        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n          data = this.selectResult;\n        } else {\n          try {\n            this.selectFn = options.select;\n            data = options.select(state.data);\n\n            if (options.structuralSharing !== false) {\n              data = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, data);\n            }\n\n            this.selectResult = data;\n            this.selectError = null;\n          } catch (selectError) {\n            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      } // Use query data\n      else {\n          data = state.data;\n        } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n\n            if (options.structuralSharing !== false) {\n              placeholderData = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n\n            this.selectError = null;\n          } catch (selectError) {\n            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n\n    var _this$options = this.options,\n        notifyOnChangeProps = _this$options.notifyOnChangeProps,\n        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, defaultNotifyOptions, notifyOptions));\n  };\n\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !(0,_retryer__WEBPACK_IMPORTED_MODULE_5__.isCancelledError)(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n\n  return QueryObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_7__.Subscribable);\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/retryer.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-query/es/core/retryer.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   Retryer: () => (/* binding */ Retryer),\n/* harmony export */   isCancelable: () => (/* binding */ isCancelable),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\n\nfunction isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nvar CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nvar Retryer = function Retryer(config) {\n  var _this = this;\n\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.sleep)(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!_focusManager__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() || !_onlineManager__WEBPACK_IMPORTED_MODULE_2__.onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  run();\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/subscribable.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/core/subscribable.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\nvar Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n\n  var _proto = Subscribable.prototype;\n\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n\n    var callback = listener || function () {\n      return undefined;\n    };\n\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n\n      _this.onUnsubscribe();\n    };\n  };\n\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n\n  return Subscribable;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9zdWJzY3JpYmFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsK0NBQStDO0FBQy9DOztBQUVBLG1EQUFtRDtBQUNuRDs7QUFFQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9jb3JlL3N1YnNjcmliYWJsZS5qcz8yY2Q0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgU3Vic2NyaWJhYmxlID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgZnVuY3Rpb24gU3Vic2NyaWJhYmxlKCkge1xuICAgIHRoaXMubGlzdGVuZXJzID0gW107XG4gIH1cblxuICB2YXIgX3Byb3RvID0gU3Vic2NyaWJhYmxlLnByb3RvdHlwZTtcblxuICBfcHJvdG8uc3Vic2NyaWJlID0gZnVuY3Rpb24gc3Vic2NyaWJlKGxpc3RlbmVyKSB7XG4gICAgdmFyIF90aGlzID0gdGhpcztcblxuICAgIHZhciBjYWxsYmFjayA9IGxpc3RlbmVyIHx8IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfTtcblxuICAgIHRoaXMubGlzdGVuZXJzLnB1c2goY2FsbGJhY2spO1xuICAgIHRoaXMub25TdWJzY3JpYmUoKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgX3RoaXMubGlzdGVuZXJzID0gX3RoaXMubGlzdGVuZXJzLmZpbHRlcihmdW5jdGlvbiAoeCkge1xuICAgICAgICByZXR1cm4geCAhPT0gY2FsbGJhY2s7XG4gICAgICB9KTtcblxuICAgICAgX3RoaXMub25VbnN1YnNjcmliZSgpO1xuICAgIH07XG4gIH07XG5cbiAgX3Byb3RvLmhhc0xpc3RlbmVycyA9IGZ1bmN0aW9uIGhhc0xpc3RlbmVycygpIHtcbiAgICByZXR1cm4gdGhpcy5saXN0ZW5lcnMubGVuZ3RoID4gMDtcbiAgfTtcblxuICBfcHJvdG8ub25TdWJzY3JpYmUgPSBmdW5jdGlvbiBvblN1YnNjcmliZSgpIHsvLyBEbyBub3RoaW5nXG4gIH07XG5cbiAgX3Byb3RvLm9uVW5zdWJzY3JpYmUgPSBmdW5jdGlvbiBvblVuc3Vic2NyaWJlKCkgey8vIERvIG5vdGhpbmdcbiAgfTtcblxuICByZXR1cm4gU3Vic2NyaWJhYmxlO1xufSgpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/types.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/types.js ***!
  \***************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   difference: () => (/* binding */ difference),\n/* harmony export */   ensureQueryKeyArray: () => (/* binding */ ensureQueryKeyArray),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getAbortController: () => (/* binding */ getAbortController),\n/* harmony export */   hashQueryKey: () => (/* binding */ hashQueryKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isQueryKey: () => (/* binding */ isQueryKey),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   mapQueryStatusFilter: () => (/* binding */ mapQueryStatusFilter),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   parseFilterArgs: () => (/* binding */ parseFilterArgs),\n/* harmony export */   parseMutationArgs: () => (/* binding */ parseMutationArgs),\n/* harmony export */   parseMutationFilterArgs: () => (/* binding */ parseMutationFilterArgs),\n/* harmony export */   parseQueryArgs: () => (/* binding */ parseQueryArgs),\n/* harmony export */   partialDeepEqual: () => (/* binding */ partialDeepEqual),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceAt: () => (/* binding */ replaceAt),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   scheduleMicrotask: () => (/* binding */ scheduleMicrotask),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   stableValueHash: () => (/* binding */ stableValueHash),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\n// TYPES\n// UTILS\nvar isServer = typeof window === 'undefined';\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction ensureQueryKeyArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nfunction difference(array1, array2) {\n  return array1.filter(function (x) {\n    return array2.indexOf(x) === -1;\n  });\n}\nfunction replaceAt(array, index, value) {\n  var copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg3, {\n      queryKey: arg1,\n      queryFn: arg2\n    });\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    queryKey: arg1\n  });\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg3, {\n        mutationKey: arg1,\n        mutationFn: arg2\n      });\n    }\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n      mutationKey: arg1\n    });\n  }\n\n  if (typeof arg1 === 'function') {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n      mutationFn: arg1\n    });\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg1);\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [(0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    queryKey: arg1\n  }), arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2) {\n  return isQueryKey(arg1) ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    mutationKey: arg1\n  }) : arg1;\n}\nfunction mapQueryStatusFilter(active, inactive) {\n  if (active === true && inactive === true || active == null && inactive == null) {\n    return 'all';\n  } else if (active === false && inactive === false) {\n    return 'none';\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    var isActive = active != null ? active : !inactive;\n    return isActive ? 'active' : 'inactive';\n  }\n}\nfunction matchQuery(filters, query) {\n  var active = filters.active,\n      exact = filters.exact,\n      fetching = filters.fetching,\n      inactive = filters.inactive,\n      predicate = filters.predicate,\n      queryKey = filters.queryKey,\n      stale = filters.stale;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  var queryStatusFilter = mapQueryStatusFilter(active, inactive);\n\n  if (queryStatusFilter === 'none') {\n    return false;\n  } else if (queryStatusFilter !== 'all') {\n    var isActive = query.isActive();\n\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false;\n    }\n\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  var exact = filters.exact,\n      fetching = filters.fetching,\n      predicate = filters.predicate,\n      mutationKey = filters.mutationKey;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  var hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n */\n\nfunction hashQueryKey(queryKey) {\n  var asArray = ensureQueryKeyArray(queryKey);\n  return stableValueHash(asArray);\n}\n/**\n * Hashes the value into a stable hash.\n */\n\nfunction stableValueHash(value) {\n  return JSON.stringify(value, function (_, val) {\n    return isPlainObject(val) ? Object.keys(val).sort().reduce(function (result, key) {\n      result[key] = val[key];\n      return result;\n    }, {}) : val;\n  });\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b));\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(function (key) {\n      return !partialDeepEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  var array = Array.isArray(a) && Array.isArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    var aSize = array ? a.length : Object.keys(a).length;\n    var bItems = array ? b : Object.keys(b);\n    var bSize = bItems.length;\n    var copy = array ? [] : {};\n    var equalItems = 0;\n\n    for (var i = 0; i < bSize; i++) {\n      var key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (var key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  var ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  var prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return typeof value === 'string' || Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(function (resolve) {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/react-query/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core */ \"(ssr)/./node_modules/react-query/es/core/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _core__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _core__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./react */ \"(ssr)/./node_modules/react-query/es/react/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _react__WEBPACK_IMPORTED_MODULE_1__) if([\"default\",\"CancelledError\",\"QueryCache\",\"QueryClient\",\"QueryObserver\",\"QueriesObserver\",\"InfiniteQueryObserver\",\"MutationCache\",\"MutationObserver\",\"setLogger\",\"notifyManager\",\"focusManager\",\"onlineManager\",\"hashQueryKey\",\"isError\",\"isCancelledError\",\"dehydrate\",\"hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _react__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvaW5kZXguanM/YWY2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2NvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9yZWFjdCc7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/Hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/react-query/es/react/Hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* binding */ Hydrate),\n/* harmony export */   useHydrate: () => (/* binding */ useHydrate)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/hydration.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\nfunction useHydrate(state, options) {\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var optionsRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(options);\n  optionsRef.current = options; // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n\n  react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {\n    if (state) {\n      (0,_core__WEBPACK_IMPORTED_MODULE_2__.hydrate)(queryClient, state, optionsRef.current);\n    }\n  }, [queryClient, state]);\n}\nvar Hydrate = function Hydrate(_ref) {\n  var children = _ref.children,\n      options = _ref.options,\n      state = _ref.state;\n  useHydrate(state, options);\n  return children;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvSHlkcmF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDUTtBQUNxQjtBQUNoRDtBQUNQLG9CQUFvQixvRUFBYztBQUNsQyxtQkFBbUIsbURBQVk7QUFDL0IsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTs7QUFFQSxFQUFFLG9EQUFhO0FBQ2Y7QUFDQSxNQUFNLDhDQUFPO0FBQ2I7QUFDQSxHQUFHO0FBQ0g7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L0h5ZHJhdGUuanM/Mzk2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaHlkcmF0ZSB9IGZyb20gJy4uL2NvcmUnO1xuaW1wb3J0IHsgdXNlUXVlcnlDbGllbnQgfSBmcm9tICcuL1F1ZXJ5Q2xpZW50UHJvdmlkZXInO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUh5ZHJhdGUoc3RhdGUsIG9wdGlvbnMpIHtcbiAgdmFyIHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKTtcbiAgdmFyIG9wdGlvbnNSZWYgPSBSZWFjdC51c2VSZWYob3B0aW9ucyk7XG4gIG9wdGlvbnNSZWYuY3VycmVudCA9IG9wdGlvbnM7IC8vIFJ1bm5pbmcgaHlkcmF0ZSBhZ2FpbiB3aXRoIHRoZSBzYW1lIHF1ZXJpZXMgaXMgc2FmZSxcbiAgLy8gaXQgd29udCBvdmVyd3JpdGUgb3IgaW5pdGlhbGl6ZSBleGlzdGluZyBxdWVyaWVzLFxuICAvLyByZWx5aW5nIG9uIHVzZU1lbW8gaGVyZSBpcyBvbmx5IGEgcGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9uLlxuICAvLyBoeWRyYXRlIGNhbiBhbmQgc2hvdWxkIGJlIHJ1biAqZHVyaW5nKiByZW5kZXIgaGVyZSBmb3IgU1NSIHRvIHdvcmsgcHJvcGVybHlcblxuICBSZWFjdC51c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoc3RhdGUpIHtcbiAgICAgIGh5ZHJhdGUocXVlcnlDbGllbnQsIHN0YXRlLCBvcHRpb25zUmVmLmN1cnJlbnQpO1xuICAgIH1cbiAgfSwgW3F1ZXJ5Q2xpZW50LCBzdGF0ZV0pO1xufVxuZXhwb3J0IHZhciBIeWRyYXRlID0gZnVuY3Rpb24gSHlkcmF0ZShfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgICBvcHRpb25zID0gX3JlZi5vcHRpb25zLFxuICAgICAgc3RhdGUgPSBfcmVmLnN0YXRlO1xuICB1c2VIeWRyYXRlKHN0YXRlLCBvcHRpb25zKTtcbiAgcmV0dXJuIGNoaWxkcmVuO1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/Hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-query/es/react/QueryClientProvider.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar defaultContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nvar useQueryClient = function useQueryClient() {\n  var queryClient = react__WEBPACK_IMPORTED_MODULE_0___default().useContext(getQueryClientContext(react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nvar QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n      _ref$contextSharing = _ref.contextSharing,\n      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n      children = _ref.children;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: client\n  }, children));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-query/es/react/QueryErrorResetBoundary.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\n\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(createValue()); // HOOK\n\nvar useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nvar QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwQixDQUFDOztBQUUzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrREFBa0QsMERBQW1CLGlCQUFpQjs7QUFFL0U7QUFDUCxTQUFTLHVEQUFnQjtBQUN6QixHQUFHOztBQUVJO0FBQ1A7QUFDQSxjQUFjLG9EQUFhO0FBQzNCO0FBQ0EsR0FBRztBQUNILHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9RdWVyeUVycm9yUmVzZXRCb3VuZGFyeS5qcz83YmEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7IC8vIENPTlRFWFRcblxuZnVuY3Rpb24gY3JlYXRlVmFsdWUoKSB7XG4gIHZhciBfaXNSZXNldCA9IGZhbHNlO1xuICByZXR1cm4ge1xuICAgIGNsZWFyUmVzZXQ6IGZ1bmN0aW9uIGNsZWFyUmVzZXQoKSB7XG4gICAgICBfaXNSZXNldCA9IGZhbHNlO1xuICAgIH0sXG4gICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkge1xuICAgICAgX2lzUmVzZXQgPSB0cnVlO1xuICAgIH0sXG4gICAgaXNSZXNldDogZnVuY3Rpb24gaXNSZXNldCgpIHtcbiAgICAgIHJldHVybiBfaXNSZXNldDtcbiAgICB9XG4gIH07XG59XG5cbnZhciBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeUNvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dChjcmVhdGVWYWx1ZSgpKTsgLy8gSE9PS1xuXG5leHBvcnQgdmFyIHVzZVF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5ID0gZnVuY3Rpb24gdXNlUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkoKSB7XG4gIHJldHVybiBSZWFjdC51c2VDb250ZXh0KFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5Q29udGV4dCk7XG59OyAvLyBDT01QT05FTlRcblxuZXhwb3J0IHZhciBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeSA9IGZ1bmN0aW9uIFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5KF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbjtcbiAgdmFyIHZhbHVlID0gUmVhY3QudXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGNyZWF0ZVZhbHVlKCk7XG4gIH0sIFtdKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5Q29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiB2YWx1ZVxuICB9LCB0eXBlb2YgY2hpbGRyZW4gPT09ICdmdW5jdGlvbicgPyBjaGlsZHJlbih2YWx1ZSkgOiBjaGlsZHJlbik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/index.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.Hydrate),\n/* harmony export */   QueryClientProvider: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider),\n/* harmony export */   QueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.QueryErrorResetBoundary),\n/* harmony export */   useHydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.useHydrate),\n/* harmony export */   useInfiniteQuery: () => (/* reexport safe */ _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__.useInfiniteQuery),\n/* harmony export */   useIsFetching: () => (/* reexport safe */ _useIsFetching__WEBPACK_IMPORTED_MODULE_4__.useIsFetching),\n/* harmony export */   useIsMutating: () => (/* reexport safe */ _useIsMutating__WEBPACK_IMPORTED_MODULE_5__.useIsMutating),\n/* harmony export */   useMutation: () => (/* reexport safe */ _useMutation__WEBPACK_IMPORTED_MODULE_6__.useMutation),\n/* harmony export */   useQueries: () => (/* reexport safe */ _useQueries__WEBPACK_IMPORTED_MODULE_8__.useQueries),\n/* harmony export */   useQuery: () => (/* reexport safe */ _useQuery__WEBPACK_IMPORTED_MODULE_7__.useQuery),\n/* harmony export */   useQueryClient: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var _setBatchUpdatesFn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setBatchUpdatesFn */ \"(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js\");\n/* harmony import */ var _setLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./setLogger */ \"(ssr)/./node_modules/react-query/es/react/setLogger.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ \"(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\");\n/* harmony import */ var _useIsFetching__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsFetching */ \"(ssr)/./node_modules/react-query/es/react/useIsFetching.js\");\n/* harmony import */ var _useIsMutating__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useIsMutating */ \"(ssr)/./node_modules/react-query/es/react/useIsMutating.js\");\n/* harmony import */ var _useMutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useMutation */ \"(ssr)/./node_modules/react-query/es/react/useMutation.js\");\n/* harmony import */ var _useQuery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useQuery */ \"(ssr)/./node_modules/react-query/es/react/useQuery.js\");\n/* harmony import */ var _useQueries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useQueries */ \"(ssr)/./node_modules/react-query/es/react/useQueries.js\");\n/* harmony import */ var _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useInfiniteQuery */ \"(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js\");\n/* harmony import */ var _Hydrate__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Hydrate */ \"(ssr)/./node_modules/react-query/es/react/Hydrate.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/react-query/es/react/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_11__) if([\"default\",\"QueryClientProvider\",\"useQueryClient\",\"QueryErrorResetBoundary\",\"useQueryErrorResetBoundary\",\"useIsFetching\",\"useIsMutating\",\"useMutation\",\"useQuery\",\"useQueries\",\"useInfiniteQuery\",\"useHydrate\",\"Hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// Side effects\n\n\n\n\n\n\n\n\n\n\n // Types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQzZCO0FBQ1I7QUFDdUQ7QUFDb0I7QUFDaEQ7QUFDQTtBQUNKO0FBQ047QUFDSTtBQUNZO0FBQ04sQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L2luZGV4LmpzPzk5MjUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gU2lkZSBlZmZlY3RzXG5pbXBvcnQgJy4vc2V0QmF0Y2hVcGRhdGVzRm4nO1xuaW1wb3J0ICcuL3NldExvZ2dlcic7XG5leHBvcnQgeyBRdWVyeUNsaWVudFByb3ZpZGVyLCB1c2VRdWVyeUNsaWVudCB9IGZyb20gJy4vUXVlcnlDbGllbnRQcm92aWRlcic7XG5leHBvcnQgeyBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeSwgdXNlUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkgfSBmcm9tICcuL1F1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5JztcbmV4cG9ydCB7IHVzZUlzRmV0Y2hpbmcgfSBmcm9tICcuL3VzZUlzRmV0Y2hpbmcnO1xuZXhwb3J0IHsgdXNlSXNNdXRhdGluZyB9IGZyb20gJy4vdXNlSXNNdXRhdGluZyc7XG5leHBvcnQgeyB1c2VNdXRhdGlvbiB9IGZyb20gJy4vdXNlTXV0YXRpb24nO1xuZXhwb3J0IHsgdXNlUXVlcnkgfSBmcm9tICcuL3VzZVF1ZXJ5JztcbmV4cG9ydCB7IHVzZVF1ZXJpZXMgfSBmcm9tICcuL3VzZVF1ZXJpZXMnO1xuZXhwb3J0IHsgdXNlSW5maW5pdGVRdWVyeSB9IGZyb20gJy4vdXNlSW5maW5pdGVRdWVyeSc7XG5leHBvcnQgeyB1c2VIeWRyYXRlLCBIeWRyYXRlIH0gZnJvbSAnLi9IeWRyYXRlJzsgLy8gVHlwZXNcblxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/logger.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-query/es/react/logger.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar logger = console;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvbG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L2xvZ2dlci5qcz9hNGUzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgbG9nZ2VyID0gY29uc29sZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-query/es/react/reactBatchedUpdates.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unstable_batchedUpdates: () => (/* binding */ unstable_batchedUpdates)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nvar unstable_batchedUpdates = (react_dom__WEBPACK_IMPORTED_MODULE_0___default().unstable_batchedUpdates);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvcmVhY3RCYXRjaGVkVXBkYXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDMUIsOEJBQThCLDBFQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L3JlYWN0QmF0Y2hlZFVwZGF0ZXMuanM/MjZmZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tJztcbmV4cG9ydCB2YXIgdW5zdGFibGVfYmF0Y2hlZFVwZGF0ZXMgPSBSZWFjdERPTS51bnN0YWJsZV9iYXRjaGVkVXBkYXRlczsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-query/es/react/setBatchUpdatesFn.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reactBatchedUpdates */ \"(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js\");\n\n\n_core__WEBPACK_IMPORTED_MODULE_0__.notifyManager.setBatchNotifyFunction(_reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0QmF0Y2hVcGRhdGVzRm4uanMiLCJtYXBwaW5ncyI6Ijs7O0FBQXdDO0FBQ3dCO0FBQ2hFLGdEQUFhLHdCQUF3Qix5RUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9zZXRCYXRjaFVwZGF0ZXNGbi5qcz83MjZiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vdGlmeU1hbmFnZXIgfSBmcm9tICcuLi9jb3JlJztcbmltcG9ydCB7IHVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzIH0gZnJvbSAnLi9yZWFjdEJhdGNoZWRVcGRhdGVzJztcbm5vdGlmeU1hbmFnZXIuc2V0QmF0Y2hOb3RpZnlGdW5jdGlvbih1bnN0YWJsZV9iYXRjaGVkVXBkYXRlcyk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/setLogger.js":
/*!********************************************************!*\
  !*** ./node_modules/react-query/es/react/setLogger.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/react/logger.js\");\n\n\n(0,_core__WEBPACK_IMPORTED_MODULE_0__.setLogger)(_logger__WEBPACK_IMPORTED_MODULE_1__.logger);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0TG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7OztBQUFvQztBQUNGO0FBQ2xDLGdEQUFTLENBQUMsMkNBQU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9zZXRMb2dnZXIuanM/OWQ0MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzZXRMb2dnZXIgfSBmcm9tICcuLi9jb3JlJztcbmltcG9ydCB7IGxvZ2dlciB9IGZyb20gJy4vbG9nZ2VyJztcbnNldExvZ2dlcihsb2dnZXIpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/setLogger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/types.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/types.js ***!
  \****************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useBaseQuery.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/react/useBaseQuery.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ \"(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/react/utils.js\");\n\n\n\n\n\nfunction useBaseQuery(options, Observer) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var errorResetBoundary = (0,_QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {\n    return new Observer(queryClient, defaultedOptions);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedOptions);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && (0,_utils__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-query/es/react/useInfiniteQuery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/infiniteQueryObserver */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ \"(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\");\n\n\n // HOOK\n\nfunction useInfiniteQuery(arg1, arg2, arg3) {\n  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(options, _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__.InfiniteQueryObserver);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlSW5maW5pdGVRdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXNFO0FBQ3ZCO0FBQ0QsQ0FBQzs7QUFFeEM7QUFDUCxnQkFBZ0IsMkRBQWM7QUFDOUIsU0FBUywyREFBWSxVQUFVLDhFQUFxQjtBQUNwRCIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L3VzZUluZmluaXRlUXVlcnkuanM/NzU1OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXIgfSBmcm9tICcuLi9jb3JlL2luZmluaXRlUXVlcnlPYnNlcnZlcic7XG5pbXBvcnQgeyBwYXJzZVF1ZXJ5QXJncyB9IGZyb20gJy4uL2NvcmUvdXRpbHMnO1xuaW1wb3J0IHsgdXNlQmFzZVF1ZXJ5IH0gZnJvbSAnLi91c2VCYXNlUXVlcnknOyAvLyBIT09LXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VJbmZpbml0ZVF1ZXJ5KGFyZzEsIGFyZzIsIGFyZzMpIHtcbiAgdmFyIG9wdGlvbnMgPSBwYXJzZVF1ZXJ5QXJncyhhcmcxLCBhcmcyLCBhcmczKTtcbiAgcmV0dXJuIHVzZUJhc2VRdWVyeShvcHRpb25zLCBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXIpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useIsFetching.js":
/*!************************************************************!*\
  !*** ./node_modules/react-query/es/react/useIsFetching.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsFetching: () => (/* binding */ useIsFetching)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\n\nvar checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {\n  var newIsFetching = queryClient.isFetching(filters);\n\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching);\n  }\n};\n\nfunction useIsFetching(arg1, arg2) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n\n  var _parseFilterArgs = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseFilterArgs)(arg1, arg2),\n      filters = _parseFilterArgs[0];\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isFetching(filters)),\n      isFetching = _React$useState[0],\n      setIsFetching = _React$useState[1];\n\n  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);\n  filtersRef.current = filters;\n  var isFetchingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isFetching);\n  isFetchingRef.current = isFetching;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n    var unsubscribe = queryClient.getQueryCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isFetching;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useIsFetching.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useIsMutating.js":
/*!************************************************************!*\
  !*** ./node_modules/react-query/es/react/useIsMutating.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMutating: () => (/* binding */ useIsMutating)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\nfunction useIsMutating(arg1, arg2) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n  var filters = (0,_core_utils__WEBPACK_IMPORTED_MODULE_1__.parseMutationFilterArgs)(arg1, arg2);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isMutating(filters)),\n      isMutating = _React$useState[0],\n      setIsMutating = _React$useState[1];\n\n  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);\n  filtersRef.current = filters;\n  var isMutatingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isMutating);\n  isMutatingRef.current = isMutating;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = queryClient.getMutationCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        var newIsMutating = queryClient.isMutating(filtersRef.current);\n\n        if (isMutatingRef.current !== newIsMutating) {\n          setIsMutating(newIsMutating);\n        }\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isMutating;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useIsMutating.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useMutation.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/react/useMutation.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/mutationObserver */ \"(ssr)/./node_modules/react-query/es/core/mutationObserver.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/react/utils.js\");\n\n\n\n\n\n\n // HOOK\n\nfunction useMutation(arg1, arg2, arg3) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseMutationArgs)(arg1, arg2, arg3);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n  var obsRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef();\n\n  if (!obsRef.current) {\n    obsRef.current = new _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__.MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n\n  var currentResult = obsRef.current.getCurrentResult();\n  react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(_core_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n  }, []);\n\n  if (currentResult.error && (0,_utils__WEBPACK_IMPORTED_MODULE_6__.shouldThrowError)(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useQueries.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-query/es/react/useQueries.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueries: () => (/* binding */ useQueries)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/queriesObserver */ \"(ssr)/./node_modules/react-query/es/core/queriesObserver.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\nfunction useQueries(queries) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var defaultedQueries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return queries.map(function (options) {\n      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options\n\n      defaultedOptions.optimisticResults = true;\n      return defaultedOptions;\n    });\n  }, [queries, queryClient]);\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {\n    return new _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__.QueriesObserver(queryClient, defaultedQueries);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedQueries);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [observer]);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, {\n      listeners: false\n    });\n  }, [defaultedQueries, observer]);\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useQueries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useQuery.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-query/es/react/useQuery.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ \"(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\");\n\n\n // HOOK\n\nfunction useQuery(arg1, arg2, arg3) {\n  var parsedOptions = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(parsedOptions, _core__WEBPACK_IMPORTED_MODULE_2__.QueryObserver);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlUXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3QztBQUNPO0FBQ0QsQ0FBQzs7QUFFeEM7QUFDUCxzQkFBc0IsMkRBQWM7QUFDcEMsU0FBUywyREFBWSxnQkFBZ0IsZ0RBQWE7QUFDbEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC91c2VRdWVyeS5qcz8xMmFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFF1ZXJ5T2JzZXJ2ZXIgfSBmcm9tICcuLi9jb3JlJztcbmltcG9ydCB7IHBhcnNlUXVlcnlBcmdzIH0gZnJvbSAnLi4vY29yZS91dGlscyc7XG5pbXBvcnQgeyB1c2VCYXNlUXVlcnkgfSBmcm9tICcuL3VzZUJhc2VRdWVyeSc7IC8vIEhPT0tcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVF1ZXJ5KGFyZzEsIGFyZzIsIGFyZzMpIHtcbiAgdmFyIHBhcnNlZE9wdGlvbnMgPSBwYXJzZVF1ZXJ5QXJncyhhcmcxLCBhcmcyLCBhcmczKTtcbiAgcmV0dXJuIHVzZUJhc2VRdWVyeShwYXJzZWRPcHRpb25zLCBRdWVyeU9ic2VydmVyKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\nfunction shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7O0FBR0osd0VBQXdFOztBQUV4RTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXRpbHMuanM/MTc0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gc2hvdWxkVGhyb3dFcnJvcihzdXNwZW5zZSwgX3VzZUVycm9yQm91bmRhcnksIHBhcmFtcykge1xuICAvLyBBbGxvdyB1c2VFcnJvckJvdW5kYXJ5IGZ1bmN0aW9uIHRvIG92ZXJyaWRlIHRocm93aW5nIGJlaGF2aW9yIG9uIGEgcGVyLWVycm9yIGJhc2lzXG4gIGlmICh0eXBlb2YgX3VzZUVycm9yQm91bmRhcnkgPT09ICdmdW5jdGlvbicpIHtcbiAgICByZXR1cm4gX3VzZUVycm9yQm91bmRhcnkuYXBwbHkodm9pZCAwLCBwYXJhbXMpO1xuICB9IC8vIEFsbG93IHVzZUVycm9yQm91bmRhcnkgdG8gb3ZlcnJpZGUgc3VzcGVuc2UncyB0aHJvd2luZyBiZWhhdmlvclxuXG5cbiAgaWYgKHR5cGVvZiBfdXNlRXJyb3JCb3VuZGFyeSA9PT0gJ2Jvb2xlYW4nKSByZXR1cm4gX3VzZUVycm9yQm91bmRhcnk7IC8vIElmIHN1c3BlbnNlIGlzIGVuYWJsZWQgZGVmYXVsdCB0byB0aHJvd2luZyBlcnJvcnNcblxuICByZXR1cm4gISFzdXNwZW5zZTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/utils.js\n");

/***/ })

};
;