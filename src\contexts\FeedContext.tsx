'use client'

import { createContext, useContext, useState, useCallback } from 'react'
import { Post } from '@/lib/supabase'

interface FeedPost extends Post {
  file?: File
  preview?: string
}

interface FeedContextType {
  posts: FeedPost[]
  selectedPost: FeedPost | null
  gridSize: '3x3' | '6x6'
  addPost: (post: FeedPost) => void
  removePost: (id: string) => void
  updatePost: (id: string, updates: Partial<FeedPost>) => void
  movePost: (fromIndex: number, toIndex: number) => void
  selectPost: (post: FeedPost | null) => void
  setGridSize: (size: '3x3' | '6x6') => void
  clearFeed: () => void
  getPostAtPosition: (position: number) => FeedPost | null
}

const FeedContext = createContext<FeedContextType | undefined>(undefined)

export function FeedProvider({ children }: { children: React.ReactNode }) {
  const [posts, setPosts] = useState<FeedPost[]>([])
  const [selectedPost, setSelectedPost] = useState<FeedPost | null>(null)
  const [gridSize, setGridSize] = useState<'3x3' | '6x6'>('3x3')

  const addPost = useCallback((post: FeedPost) => {
    setPosts(prev => [...prev, { ...post, position: prev.length }])
  }, [])

  const removePost = useCallback((id: string) => {
    setPosts(prev => prev.filter(post => post.id !== id))
    if (selectedPost?.id === id) {
      setSelectedPost(null)
    }
  }, [selectedPost])

  const updatePost = useCallback((id: string, updates: Partial<FeedPost>) => {
    setPosts(prev => prev.map(post => 
      post.id === id ? { ...post, ...updates } : post
    ))
    if (selectedPost?.id === id) {
      setSelectedPost(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedPost])

  const movePost = useCallback((fromIndex: number, toIndex: number) => {
    setPosts(prev => {
      const newPosts = [...prev]
      const [movedPost] = newPosts.splice(fromIndex, 1)
      newPosts.splice(toIndex, 0, movedPost)
      
      // Update positions
      return newPosts.map((post, index) => ({ ...post, position: index }))
    })
  }, [])

  const selectPost = useCallback((post: FeedPost | null) => {
    setSelectedPost(post)
  }, [])

  const clearFeed = useCallback(() => {
    setPosts([])
    setSelectedPost(null)
  }, [])

  const getPostAtPosition = useCallback((position: number) => {
    return posts.find(post => post.position === position) || null
  }, [posts])

  const value = {
    posts,
    selectedPost,
    gridSize,
    addPost,
    removePost,
    updatePost,
    movePost,
    selectPost,
    setGridSize,
    clearFeed,
    getPostAtPosition,
  }

  return (
    <FeedContext.Provider value={value}>
      {children}
    </FeedContext.Provider>
  )
}

export function useFeed() {
  const context = useContext(FeedContext)
  if (context === undefined) {
    throw new Error('useFeed must be used within a FeedProvider')
  }
  return context
}
