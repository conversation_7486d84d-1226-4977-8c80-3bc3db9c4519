'use client'

import { createContext, useContext, useState, useCallback } from 'react'
import { Post } from '@/lib/supabase'

interface FeedPost extends Post {
  file?: File
  preview?: string
}

interface FeedContextType {
  posts: FeedPost[]
  selectedPost: FeedPost | null
  gridSize: '3x3' | '6x6'
  addPost: (post: FeedPost) => void
  removePost: (id: string) => void
  updatePost: (id: string, updates: Partial<FeedPost>) => void
  movePost: (fromIndex: number, toIndex: number) => void
  selectPost: (post: FeedPost | null) => void
  setGridSize: (size: '3x3' | '6x6') => void
  clearFeed: () => void
  getPostAtPosition: (position: number) => FeedPost | null
}

const FeedContext = createContext<FeedContextType | undefined>(undefined)

export function FeedProvider({ children }: { children: React.ReactNode }) {
  const [posts, setPosts] = useState<FeedPost[]>([])
  const [selectedPost, setSelectedPost] = useState<FeedPost | null>(null)
  const [gridSize, setGridSize] = useState<'3x3' | '6x6'>('3x3')

  const addPost = useCallback((post: FeedPost) => {
    setPosts(prev => {
      // Find the first available position
      const maxGridSize = gridSize === '3x3' ? 9 : 36
      const occupiedPositions = new Set(prev.map(p => p.position))

      let nextPosition = 0
      while (occupiedPositions.has(nextPosition) && nextPosition < maxGridSize) {
        nextPosition++
      }

      return [...prev, { ...post, position: nextPosition }]
    })
  }, [gridSize])

  const removePost = useCallback((id: string) => {
    setPosts(prev => prev.filter(post => post.id !== id))
    if (selectedPost?.id === id) {
      setSelectedPost(null)
    }
  }, [selectedPost])

  const updatePost = useCallback((id: string, updates: Partial<FeedPost>) => {
    setPosts(prev => prev.map(post =>
      post.id === id ? { ...post, ...updates } : post
    ))
    if (selectedPost?.id === id) {
      setSelectedPost(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedPost])

  const movePost = useCallback((fromPosition: number, toPosition: number) => {
    console.log('Moving post from position', fromPosition, 'to position', toPosition)

    setPosts(prev => {
      console.log('Current posts before move:', prev.map(p => ({ id: p.id, position: p.position, hasImage: !!p.preview })))

      // Find posts at the specified positions
      const fromPost = prev.find(post => post.position === fromPosition)
      const toPost = prev.find(post => post.position === toPosition)

      console.log('From post:', fromPost?.id, 'To post:', toPost?.id)

      if (!fromPost) {
        console.log('No post found at from position', fromPosition)
        return prev
      }

      // If there's no post at the target position, just move the post there
      if (!toPost) {
        const newPosts = prev.map(post =>
          post.position === fromPosition
            ? { ...post, position: toPosition }
            : post
        )
        console.log('Moved to empty position. New posts:', newPosts.map(p => ({ id: p.id, position: p.position })))
        return newPosts
      }

      // If there's a post at the target position, swap them
      const newPosts = prev.map(post => {
        if (post.position === fromPosition) {
          return { ...post, position: toPosition }
        }
        if (post.position === toPosition) {
          return { ...post, position: fromPosition }
        }
        return post
      })

      console.log('Swapped positions. New posts:', newPosts.map(p => ({ id: p.id, position: p.position, hasImage: !!p.preview })))
      return newPosts
    })
  }, [])

  const selectPost = useCallback((post: FeedPost | null) => {
    setSelectedPost(post)
  }, [])

  const clearFeed = useCallback(() => {
    setPosts([])
    setSelectedPost(null)
  }, [])

  const getPostAtPosition = useCallback((position: number) => {
    return posts.find(post => post.position === position) || null
  }, [posts])

  const value = {
    posts,
    selectedPost,
    gridSize,
    addPost,
    removePost,
    updatePost,
    movePost,
    selectPost,
    setGridSize,
    clearFeed,
    getPostAtPosition,
  }

  return (
    <FeedContext.Provider value={value}>
      {children}
    </FeedContext.Provider>
  )
}

export function useFeed() {
  const context = useContext(FeedContext)
  if (context === undefined) {
    throw new Error('useFeed must be used within a FeedProvider')
  }
  return context
}
