'use client'

import { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { Post } from '@/lib/supabase'

interface FeedPost extends Post {
  file?: File
  preview?: string
}

interface FeedContextType {
  posts: FeedPost[]
  selectedPost: FeedPost | null
  gridSize: '3x3' | '6x6'
  currentProject: string | null
  saving: boolean
  addPost: (post: FeedPost) => void
  removePost: (id: string) => void
  updatePost: (id: string, updates: Partial<FeedPost>) => void
  movePost: (fromIndex: number, toIndex: number) => void
  selectPost: (post: FeedPost | null) => void
  setGridSize: (size: '3x3' | '6x6') => void
  clearFeed: () => void
  getPostAtPosition: (position: number) => FeedPost | null
  saveFeed: (name?: string) => Promise<void>
  loadFeed: (projectId: string) => Promise<void>
  autoSave: () => void
}

const FeedContext = createContext<FeedContextType | undefined>(undefined)

export function FeedProvider({ children }: { children: React.ReactNode }) {
  const [posts, setPosts] = useState<FeedPost[]>([])
  const [selectedPost, setSelectedPost] = useState<FeedPost | null>(null)
  const [gridSize, setGridSize] = useState<'3x3' | '6x6'>('3x3')
  const [currentProject, setCurrentProject] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)

  const addPost = useCallback((post: FeedPost) => {
    setPosts(prev => {
      // Find the first available position
      const maxGridSize = gridSize === '3x3' ? 9 : 36
      const occupiedPositions = new Set(prev.map(p => p.position))

      let nextPosition = 0
      while (occupiedPositions.has(nextPosition) && nextPosition < maxGridSize) {
        nextPosition++
      }

      return [...prev, { ...post, position: nextPosition }]
    })
  }, [gridSize])

  const removePost = useCallback((id: string) => {
    setPosts(prev => prev.filter(post => post.id !== id))
    if (selectedPost?.id === id) {
      setSelectedPost(null)
    }
  }, [selectedPost])

  const updatePost = useCallback((id: string, updates: Partial<FeedPost>) => {
    setPosts(prev => prev.map(post =>
      post.id === id ? { ...post, ...updates } : post
    ))
    if (selectedPost?.id === id) {
      setSelectedPost(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedPost])

  const movePost = useCallback((fromPosition: number, toPosition: number) => {
    console.log('Moving post from position', fromPosition, 'to position', toPosition)

    setPosts(prev => {
      console.log('Current posts before move:', prev.map(p => ({ id: p.id, position: p.position, hasImage: !!p.preview })))

      // Find posts at the specified positions
      const fromPost = prev.find(post => post.position === fromPosition)
      const toPost = prev.find(post => post.position === toPosition)

      console.log('From post:', fromPost?.id, 'To post:', toPost?.id)

      if (!fromPost) {
        console.log('No post found at from position', fromPosition)
        return prev
      }

      // If there's no post at the target position, just move the post there
      if (!toPost) {
        const newPosts = prev.map(post =>
          post.position === fromPosition
            ? { ...post, position: toPosition }
            : post
        )
        console.log('Moved to empty position. New posts:', newPosts.map(p => ({ id: p.id, position: p.position })))
        return newPosts
      }

      // If there's a post at the target position, swap them
      const newPosts = prev.map(post => {
        if (post.position === fromPosition) {
          return { ...post, position: toPosition }
        }
        if (post.position === toPosition) {
          return { ...post, position: fromPosition }
        }
        return post
      })

      console.log('Swapped positions. New posts:', newPosts.map(p => ({ id: p.id, position: p.position, hasImage: !!p.preview })))
      return newPosts
    })
  }, [])

  const selectPost = useCallback((post: FeedPost | null) => {
    setSelectedPost(post)
  }, [])

  const clearFeed = useCallback(() => {
    setPosts([])
    setSelectedPost(null)
  }, [])

  const getPostAtPosition = useCallback((position: number) => {
    return posts.find(post => post.position === position) || null
  }, [posts])

  // Save feed to localStorage (and eventually Supabase)
  const saveFeed = useCallback(async (name?: string) => {
    setSaving(true)
    try {
      const projectData = {
        id: currentProject || `project_${Date.now()}`,
        name: name || `Feed ${new Date().toLocaleDateString()}`,
        posts: posts,
        gridSize: gridSize,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // Save to localStorage for now
      const savedProjects = JSON.parse(localStorage.getItem('visualvibe_projects') || '[]')
      const existingIndex = savedProjects.findIndex((p: any) => p.id === projectData.id)

      if (existingIndex >= 0) {
        savedProjects[existingIndex] = projectData
      } else {
        savedProjects.push(projectData)
      }

      localStorage.setItem('visualvibe_projects', JSON.stringify(savedProjects))
      setCurrentProject(projectData.id)

      console.log('Feed saved successfully!', projectData.name)
    } catch (error) {
      console.error('Failed to save feed:', error)
    } finally {
      setSaving(false)
    }
  }, [posts, gridSize, currentProject])

  // Load feed from localStorage (and eventually Supabase)
  const loadFeed = useCallback(async (projectId: string) => {
    try {
      const savedProjects = JSON.parse(localStorage.getItem('visualvibe_projects') || '[]')
      const project = savedProjects.find((p: any) => p.id === projectId)

      if (project) {
        setPosts(project.posts || [])
        setGridSize(project.gridSize || '3x3')
        setCurrentProject(project.id)
        console.log('Feed loaded successfully!', project.name)
      }
    } catch (error) {
      console.error('Failed to load feed:', error)
    }
  }, [])

  // Auto-save functionality
  const autoSave = useCallback(() => {
    if (posts.length > 0 && currentProject) {
      saveFeed()
    }
  }, [posts, saveFeed, currentProject])

  // Auto-save when posts change (with debounce)
  useEffect(() => {
    if (currentProject && posts.length > 0) {
      const timeoutId = setTimeout(() => {
        saveFeed()
      }, 2000) // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId)
    }
  }, [posts, currentProject, saveFeed])

  // Load saved feed on mount
  useEffect(() => {
    const savedProjects = JSON.parse(localStorage.getItem('visualvibe_projects') || '[]')
    if (savedProjects.length > 0) {
      // Load the most recent project
      const mostRecent = savedProjects.sort((a: any, b: any) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )[0]

      if (mostRecent) {
        loadFeed(mostRecent.id)
      }
    }
  }, [])

  const value = {
    posts,
    selectedPost,
    gridSize,
    currentProject,
    saving,
    addPost,
    removePost,
    updatePost,
    movePost,
    selectPost,
    setGridSize,
    clearFeed,
    getPostAtPosition,
    saveFeed,
    loadFeed,
    autoSave,
  }

  return (
    <FeedContext.Provider value={value}>
      {children}
    </FeedContext.Provider>
  )
}

export function useFeed() {
  const context = useContext(FeedContext)
  if (context === undefined) {
    throw new Error('useFeed must be used within a FeedProvider')
  }
  return context
}
