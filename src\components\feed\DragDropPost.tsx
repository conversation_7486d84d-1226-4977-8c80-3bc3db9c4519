'use client'

import { useDrag } from 'react-dnd'
import { useFeed } from '@/contexts/FeedContext'
import { Post } from '@/lib/supabase'
import { X, Edit3, Heart, MessageCircle } from 'lucide-react'
import Image from 'next/image'

interface DragDropPostProps {
  post: Post & { file?: File; preview?: string }
  position: number
}

export function DragDropPost({ post, position }: DragDropPostProps) {
  const { removePost, selectPost, selectedPost } = useFeed()

  const [{ isDragging }, drag] = useDrag({
    type: 'post',
    item: { position: post.position, id: post.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const isSelected = selectedPost?.id === post.id
  const imageUrl = post.preview || post.image_url

  // Debug logging (commented out for cleaner console)
  // console.log('DragDropPost render:', {
  //   postId: post.id,
  //   position: post.position,
  //   hasPreview: !!post.preview,
  //   hasImageUrl: !!post.image_url,
  //   finalImageUrl: imageUrl
  // })

  const handleClick = () => {
    selectPost(post)
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    removePost(post.id)
  }

  return (
    <div
      ref={drag}
      className={`
        w-full h-full relative cursor-pointer group
        ${isDragging ? 'dragging' : ''}
        ${isSelected ? 'ring-2 ring-primary-500' : ''}
        transition-all duration-200
      `}
      onClick={handleClick}
    >
      {/* Image */}
      <div className="w-full h-full relative overflow-hidden rounded-lg">
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={post.caption || 'Instagram post'}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            onError={(e) => {
              console.log('Image failed to load:', imageUrl, 'for post:', post.id)
              console.log('Post data:', { preview: post.preview, image_url: post.image_url })
              // Don't try to change src as it can cause infinite loops
              // Instead, we'll handle this in the component state
            }}
            unoptimized={imageUrl.startsWith('blob:') || imageUrl.startsWith('data:')}
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-400 text-xs">No image</span>
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
            <button
              className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
              onClick={(e) => {
                e.stopPropagation()
                selectPost(post)
              }}
            >
              <Edit3 className="h-4 w-4 text-gray-700" />
            </button>
            <button
              className="p-2 bg-red-500 bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
              onClick={handleRemove}
            >
              <X className="h-4 w-4 text-white" />
            </button>
          </div>
        </div>

        {/* Instagram-style engagement overlay */}
        {(post.engagement_score || post.aesthetic_score) && (
          <div className="absolute bottom-2 left-2 right-2">
            <div className="bg-black bg-opacity-50 rounded px-2 py-1 flex items-center justify-between text-white text-xs">
              {post.engagement_score && (
                <div className="flex items-center space-x-1">
                  <Heart className="h-3 w-3" />
                  <span>{Math.round(post.engagement_score * 100)}</span>
                </div>
              )}
              {post.aesthetic_score && (
                <div className="flex items-center space-x-1">
                  <span>✨</span>
                  <span>{post.aesthetic_score.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Caption preview */}
        {post.caption && (
          <div className="absolute top-2 left-2 right-2">
            <div className="bg-black bg-opacity-50 rounded px-2 py-1 text-white text-xs truncate">
              {post.caption}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
