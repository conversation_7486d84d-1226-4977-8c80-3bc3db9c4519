{"version": 3, "sources": ["../../src/lib/format-cli-help-output.ts"], "names": ["formatCliHelpOutput", "cmd", "helper", "termWidth", "<PERSON><PERSON><PERSON><PERSON>", "helpWidth", "itemIndentWidth", "itemSeparatorWidth", "formatItem", "term", "description", "value", "fullText", "padEnd", "wrap", "formatList", "textArray", "join", "replace", "repeat", "output", "bold", "commandUsage", "commandDescription", "length", "concat", "argumentList", "visibleArguments", "map", "argument", "argumentTerm", "argumentDescription", "optionList", "visibleOptions", "option", "optionTerm", "optionDescription", "commandList", "visibleCommands", "subCmd", "subcommandTerm", "subcommandDescription"], "mappings": ";;;;+BA+FSA;;;eAAAA;;;4BA9FY;AAErB,2DAA2D;AAC3D,mEAAmE;AACnE,mEAAmE;AACnE,MAAMA,sBAAsB,CAACC,KAAcC;IACzC,MAAMC,YAAYD,OAAOE,QAAQ,CAACH,KAAKC;IACvC,MAAMG,YAAYH,OAAOG,SAAS,IAAI;IACtC,MAAMC,kBAAkB;IACxB,MAAMC,qBAAqB,EAAE,+BAA+B;;IAE5D,SAASC,WAAWC,IAAY,EAAEC,WAAmB;QACnD,IAAIC,QAAQF;QAEZ,IAAIC,aAAa;YACf,IAAID,SAAS,aAAa;gBACxBE,QAAQ,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;YACrB;YAEA,MAAMG,WAAW,CAAC,EAAED,MAAME,MAAM,CAC9BV,YAAYI,oBACZ,EAAEG,YAAY,CAAC;YAEjB,OAAOR,OAAOY,IAAI,CAChBF,UACAP,YAAYC,iBACZH,YAAYI;QAEhB;QAEA,OAAOE;IACT;IAEA,SAASM,WAAWC,SAAmB;QACrC,OAAOA,UAAUC,IAAI,CAAC,MAAMC,OAAO,CAAC,OAAO,IAAIC,MAAM,CAACb;IACxD;IAEA,QAAQ;IACR,IAAIc,SAAS;QAAC,CAAC,EAAEC,IAAAA,gBAAI,EAAC,UAAU,CAAC,EAAEnB,OAAOoB,YAAY,CAACrB,KAAK,CAAC;QAAE;KAAG;IAElE,cAAc;IACd,MAAMsB,qBAAqBrB,OAAOqB,kBAAkB,CAACtB;IAErD,IAAIsB,mBAAmBC,MAAM,GAAG,GAAG;QACjCJ,SAASA,OAAOK,MAAM,CAAC;YAACvB,OAAOY,IAAI,CAACS,oBAAoBlB,WAAW;YAAI;SAAG;IAC5E;IAEA,YAAY;IACZ,MAAMqB,eAAexB,OAAOyB,gBAAgB,CAAC1B,KAAK2B,GAAG,CAAC,CAACC;QACrD,OAAOrB,WACLN,OAAO4B,YAAY,CAACD,WACpB3B,OAAO6B,mBAAmB,CAACF;IAE/B;IAEA,IAAIH,aAAaF,MAAM,GAAG,GAAG;QAC3BJ,SAASA,OAAOK,MAAM,CAAC;YACrB,CAAC,EAAEJ,IAAAA,gBAAI,EAAC,cAAc,CAAC;YACvBN,WAAWW;YACX;SACD;IACH;IAEA,UAAU;IACV,MAAMM,aAAa9B,OAAO+B,cAAc,CAAChC,KAAK2B,GAAG,CAAC,CAACM;QACjD,OAAO1B,WACLN,OAAOiC,UAAU,CAACD,SAClBhC,OAAOkC,iBAAiB,CAACF;IAE7B;IAEA,IAAIF,WAAWR,MAAM,GAAG,GAAG;QACzBJ,SAASA,OAAOK,MAAM,CAAC;YAAC,CAAC,EAAEJ,IAAAA,gBAAI,EAAC,YAAY,CAAC;YAAEN,WAAWiB;YAAa;SAAG;IAC5E;IAEA,WAAW;IACX,MAAMK,cAAcnC,OAAOoC,eAAe,CAACrC,KAAK2B,GAAG,CAAC,CAACW;QACnD,OAAO/B,WACLN,OAAOsC,cAAc,CAACD,SACtBrC,OAAOuC,qBAAqB,CAACF;IAEjC;IAEA,IAAIF,YAAYb,MAAM,GAAG,GAAG;QAC1BJ,SAASA,OAAOK,MAAM,CAAC;YACrB,CAAC,EAAEJ,IAAAA,gBAAI,EAAC,aAAa,CAAC;YACtBN,WAAWsB;YACX;SACD;IACH;IAEA,OAAOjB,OAAOH,IAAI,CAAC;AACrB"}