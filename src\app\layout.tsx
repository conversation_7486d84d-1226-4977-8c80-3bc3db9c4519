import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'VisualVibe - Instagram Feed Curator',
  description: 'Plan, curate, and optimize your Instagram feed with AI-powered insights',
  keywords: ['Instagram', 'feed planner', 'social media', 'content curation', 'AI'],
  authors: [{ name: 'VisualVibe Team' }],
  openGraph: {
    title: 'VisualVibe - Instagram Feed Curator',
    description: 'Plan, curate, and optimize your Instagram feed with AI-powered insights',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'VisualVibe - Instagram Feed Curator',
    description: 'Plan, curate, and optimize your Instagram feed with AI-powered insights',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
