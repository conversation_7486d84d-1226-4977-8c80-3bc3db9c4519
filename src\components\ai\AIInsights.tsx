'use client'

import { useState, useEffect } from 'react'
import { Post } from '@/lib/supabase'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>rendingUp, 
  <PERSON><PERSON>, 
  Target,
  AlertT<PERSON>gle,
  CheckCircle,
  BarChart3,
  Eye
} from 'lucide-react'

interface AIInsightsProps {
  posts: Post[]
}

interface ColorAnalysis {
  dominantColors: string[]
  harmony: number
  diversity: number
}

interface EngagementPrediction {
  score: number
  factors: string[]
  suggestions: string[]
}

interface AestheticScore {
  overall: number
  composition: number
  colorHarmony: number
  consistency: number
}

export function AIInsights({ posts }: AIInsightsProps) {
  const [colorAnalysis, setColorAnalysis] = useState<ColorAnalysis | null>(null)
  const [engagementPrediction, setEngagementPrediction] = useState<EngagementPrediction | null>(null)
  const [aestheticScore, setAestheticScore] = useState<AestheticScore | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (posts.length > 0) {
      analyzeFeeds()
    }
  }, [posts])

  const analyzeFeeds = async () => {
    setLoading(true)
    
    // Simulate AI analysis (in real app, this would call actual AI services)
    setTimeout(() => {
      // Mock color analysis
      setColorAnalysis({
        dominantColors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
        harmony: Math.random() * 40 + 60, // 60-100
        diversity: Math.random() * 30 + 70, // 70-100
      })

      // Mock engagement prediction
      setEngagementPrediction({
        score: Math.random() * 30 + 70, // 70-100
        factors: [
          'Strong color consistency',
          'Good posting frequency',
          'High-quality images',
          'Engaging captions'
        ],
        suggestions: [
          'Add more user-generated content',
          'Use trending hashtags',
          'Post during peak hours (7-9 PM)',
          'Include more behind-the-scenes content'
        ]
      })

      // Mock aesthetic score
      setAestheticScore({
        overall: Math.random() * 20 + 80, // 80-100
        composition: Math.random() * 25 + 75,
        colorHarmony: Math.random() * 20 + 80,
        consistency: Math.random() * 15 + 85,
      })

      setLoading(false)
    }, 1500)
  }

  if (posts.length === 0) {
    return (
      <div className="text-center py-8">
        <Sparkles className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">Upload some posts to get AI insights</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-500">Analyzing your feed...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      {aestheticScore && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900 flex items-center">
              <Sparkles className="h-4 w-4 mr-2 text-purple-600" />
              Aesthetic Score
            </h4>
            <span className="text-2xl font-bold text-purple-600">
              {aestheticScore.overall.toFixed(1)}
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Composition</span>
              <span>{aestheticScore.composition.toFixed(1)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Color Harmony</span>
              <span>{aestheticScore.colorHarmony.toFixed(1)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Consistency</span>
              <span>{aestheticScore.consistency.toFixed(1)}</span>
            </div>
          </div>
        </div>
      )}

      {/* Color Analysis */}
      {colorAnalysis && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900 flex items-center">
            <Palette className="h-4 w-4 mr-2" />
            Color Analysis
          </h4>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-sm text-gray-600">Dominant Colors:</span>
            </div>
            <div className="flex space-x-2 mb-4">
              {colorAnalysis.dominantColors.map((color, index) => (
                <div
                  key={index}
                  className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Harmony Score</span>
                <div className="flex items-center mt-1">
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${colorAnalysis.harmony}%` }}
                    />
                  </div>
                  <span className="font-medium">{colorAnalysis.harmony.toFixed(0)}%</span>
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Diversity</span>
                <div className="flex items-center mt-1">
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${colorAnalysis.diversity}%` }}
                    />
                  </div>
                  <span className="font-medium">{colorAnalysis.diversity.toFixed(0)}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Engagement Prediction */}
      {engagementPrediction && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900 flex items-center">
            <TrendingUp className="h-4 w-4 mr-2" />
            Engagement Prediction
          </h4>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-gray-600">Predicted Score</span>
              <span className="text-lg font-bold text-green-600">
                {engagementPrediction.score.toFixed(0)}%
              </span>
            </div>
            
            <div className="space-y-3">
              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                  <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                  Positive Factors
                </h5>
                <ul className="text-xs text-gray-600 space-y-1">
                  {engagementPrediction.factors.map((factor, index) => (
                    <li key={index}>• {factor}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                  <Target className="h-4 w-4 mr-1 text-blue-500" />
                  Suggestions
                </h5>
                <ul className="text-xs text-gray-600 space-y-1">
                  {engagementPrediction.suggestions.map((suggestion, index) => (
                    <li key={index}>• {suggestion}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="space-y-2">
        <h4 className="font-medium text-gray-900">Quick Actions</h4>
        <div className="grid grid-cols-1 gap-2">
          <button className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-sm">Optimize color balance</span>
            <Palette className="h-4 w-4 text-gray-400" />
          </button>
          
          <button className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-sm">Suggest posting times</span>
            <BarChart3 className="h-4 w-4 text-gray-400" />
          </button>
          
          <button className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-sm">Preview feed impact</span>
            <Eye className="h-4 w-4 text-gray-400" />
          </button>
        </div>
      </div>
    </div>
  )
}
