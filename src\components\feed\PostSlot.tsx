'use client'

import { useDrop } from 'react-dnd'
import { useFeed } from '@/contexts/FeedContext'
import { Plus, Image as ImageIcon } from 'lucide-react'

interface PostSlotProps {
  position: number
  isEmpty: boolean
  children?: React.ReactNode
}

export function PostSlot({ position, isEmpty, children }: PostSlotProps) {
  const { movePost, getPostAtPosition } = useFeed()

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: 'post',
    drop: (item: { position: number }) => {
      if (item.position !== position) {
        movePost(item.position, position)
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  })

  const isActive = isOver && canDrop

  return (
    <div
      ref={drop}
      className={`
        post-slot aspect-square relative
        ${isEmpty ? '' : 'occupied'}
        ${isActive ? 'drag-over' : ''}
        transition-all duration-200 ease-in-out
      `}
    >
      {isEmpty ? (
        <div className="w-full h-full flex flex-col items-center justify-center text-gray-400">
          {isActive ? (
            <div className="text-blue-500">
              <ImageIcon className="h-8 w-8 mb-2" />
              <span className="text-xs">Drop here</span>
            </div>
          ) : (
            <div>
              <Plus className="h-6 w-6 mb-1" />
              <span className="text-xs">Add post</span>
            </div>
          )}
        </div>
      ) : (
        children
      )}
      
      {/* Position indicator (for debugging) */}
      <div className="absolute top-1 left-1 w-4 h-4 bg-black bg-opacity-20 rounded text-white text-xs flex items-center justify-center">
        {position + 1}
      </div>
    </div>
  )
}
