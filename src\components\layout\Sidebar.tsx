'use client'

import { useState } from 'react'
import {
  Grid3X3,
  Upload,
  Layout,
  BarChart3,
  Calendar,
  Settings,
  FolderOpen,
  Sparkles,
  Instagram
} from 'lucide-react'

const navigation = [
  { name: 'My Feeds', icon: Grid3X3, href: '/', current: true },
  { name: 'Upload New', icon: Upload, href: '/upload', current: false },
  { name: 'Templates', icon: Layout, href: '/templates', current: false },
  { name: 'Analytics', icon: BarChart3, href: '/analytics', current: false },
  { name: 'Schedule', icon: Calendar, href: '/schedule', current: false },
  { name: 'AI Insights', icon: Sparkles, href: '/insights', current: false },
  { name: 'Instagram', icon: Instagram, href: '/instagram', current: false },
]

const projects = [
  { name: 'Summer Collection', posts: 12, updated: '2 hours ago' },
  { name: 'Product Launch', posts: 8, updated: '1 day ago' },
  { name: 'Brand Story', posts: 15, updated: '3 days ago' },
]

export function Sidebar() {
  const [currentPage, setCurrentPage] = useState('My Feeds')

  return (
    <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:pt-16">
      <div className="flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200">
        <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
          <nav className="mt-5 flex-1 px-2 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = item.name === currentPage

              return (
                <button
                  key={item.name}
                  className={`sidebar-nav-item ${isActive ? 'active' : ''}`}
                  onClick={() => setCurrentPage(item.name)}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </button>
              )
            })}
          </nav>

          {/* Recent Projects */}
          <div className="mt-8 px-2">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Recent Projects
              </h3>
              <button className="text-gray-400 hover:text-gray-600">
                <FolderOpen className="h-4 w-4" />
              </button>
            </div>
            <div className="space-y-2">
              {projects.map((project) => (
                <button
                  key={project.name}
                  className="w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {project.name}
                    </span>
                    <span className="text-xs text-gray-500">
                      {project.posts}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Updated {project.updated}
                  </p>
                </button>
              ))}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-8 px-2">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                This Month
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Posts Created</span>
                  <span className="font-medium">24</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Feeds Planned</span>
                  <span className="font-medium">6</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">AI Suggestions</span>
                  <span className="font-medium">18</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
