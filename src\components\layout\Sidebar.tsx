'use client'

import { useState, useEffect } from 'react'
import {
  Grid3X3,
  Upload,
  Layout,
  BarChart3,
  Calendar,
  Settings,
  FolderOpen,
  Sparkles,
  Instagram,
  ImageIcon
} from 'lucide-react'

const navigation = [
  { name: 'My Feeds', icon: Grid3X3, href: '/', current: true },
  { name: 'Media Library', icon: ImageIcon, href: '/media', current: false },
  { name: 'Templates', icon: Layout, href: '/templates', current: false },
  { name: 'Analytics', icon: BarChart3, href: '/analytics', current: false },
  { name: 'Schedule', icon: Calendar, href: '/schedule', current: false },
  { name: 'AI Insights', icon: Sparkles, href: '/insights', current: false },
  { name: 'Instagram', icon: Instagram, href: '/instagram', current: false },
]

// This will be replaced with actual saved projects
const getSavedProjects = () => {
  try {
    const saved = JSON.parse(localStorage.getItem('visualvibe_projects') || '[]')
    return saved.slice(0, 3) // Show only the 3 most recent
  } catch {
    return []
  }
}

interface SidebarProps {
  onNavigate: (view: 'feed' | 'media') => void
  currentView: 'feed' | 'media'
}

export function Sidebar({ onNavigate, currentView }: SidebarProps) {
  const [currentPage, setCurrentPage] = useState('My Feeds')
  const [savedProjects, setSavedProjects] = useState<any[]>([])

  // Load saved projects on mount and when localStorage changes
  useEffect(() => {
    const loadProjects = () => {
      setSavedProjects(getSavedProjects())
    }

    loadProjects()

    // Listen for storage changes
    window.addEventListener('storage', loadProjects)

    // Also check periodically for changes (since localStorage events don't fire in same tab)
    const interval = setInterval(loadProjects, 1000)

    return () => {
      window.removeEventListener('storage', loadProjects)
      clearInterval(interval)
    }
  }, [])

  return (
    <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:pt-16">
      <div className="flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200">
        <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
          <nav className="mt-5 flex-1 px-2 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = item.name === currentPage

              return (
                <button
                  key={item.name}
                  className={`sidebar-nav-item ${isActive ? 'active' : ''}`}
                  onClick={() => {
                    setCurrentPage(item.name)
                    if (item.name === 'My Feeds') {
                      onNavigate('feed')
                    } else if (item.name === 'Media Library') {
                      onNavigate('media')
                    }
                  }}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </button>
              )
            })}
          </nav>

          {/* Recent Projects */}
          <div className="mt-8 px-2">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Recent Projects
              </h3>
              <button className="text-gray-400 hover:text-gray-600">
                <FolderOpen className="h-4 w-4" />
              </button>
            </div>
            <div className="space-y-2">
              {savedProjects.length > 0 ? (
                savedProjects.map((project) => (
                  <button
                    key={project.id}
                    className="w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 truncate">
                        {project.name}
                      </span>
                      <span className="text-xs text-gray-500">
                        {project.posts?.length || 0}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Updated {new Date(project.updatedAt).toLocaleDateString()}
                    </p>
                  </button>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-xs text-gray-500">No saved projects yet</p>
                  <p className="text-xs text-gray-400">Upload images and save your first feed!</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-8 px-2">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                This Month
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Posts Created</span>
                  <span className="font-medium">24</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Feeds Planned</span>
                  <span className="font-medium">6</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">AI Suggestions</span>
                  <span className="font-medium">18</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
