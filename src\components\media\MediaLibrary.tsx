'use client'

import { useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { useMediaLibrary } from '@/contexts/MediaLibraryContext'
import { useFeed } from '@/contexts/FeedContext'
import { 
  Upload, 
  Search, 
  X, 
  Plus,
  Trash2,
  Image as ImageIcon,
  Check
} from 'lucide-react'
import Image from 'next/image'
import { v4 as uuidv4 } from 'uuid'

export function MediaLibrary() {
  const { 
    mediaItems, 
    selectedItems, 
    uploading, 
    addMediaItems, 
    removeMediaItem,
    selectItem,
    deselectItem,
    clearSelection,
    searchItems 
  } = useMediaLibrary()
  
  const { addPost } = useFeed()
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const filteredItems = searchQuery ? searchItems(searchQuery) : mediaItems

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: addMediaItems,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.heic']
    },
    multiple: true,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const handleAddToFeed = (mediaItem: any) => {
    const newPost = {
      id: uuidv4(),
      user_id: '',
      image_url: mediaItem.preview,
      preview: mediaItem.preview,
      file: mediaItem.file,
      caption: '',
      hashtags: [],
      position: 0,
    }
    
    addPost(newPost)
    console.log('Added to feed:', mediaItem.name)
  }

  const handleAddSelectedToFeed = () => {
    selectedItems.forEach(itemId => {
      const mediaItem = mediaItems.find(item => item.id === itemId)
      if (mediaItem) {
        handleAddToFeed(mediaItem)
      }
    })
    clearSelection()
  }

  const handleDeleteSelected = () => {
    if (confirm(`Delete ${selectedItems.length} selected items?`)) {
      selectedItems.forEach(removeMediaItem)
      clearSelection()
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Media Library</h2>
          <span className="text-sm text-gray-500">
            {mediaItems.length} items
          </span>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search images..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
          />
        </div>

        {/* Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center space-x-2 mb-4">
            <button
              onClick={handleAddSelectedToFeed}
              className="flex items-center space-x-1 px-3 py-1 bg-primary-600 text-white rounded text-sm hover:bg-primary-700 transition-colors"
            >
              <Plus className="h-3 w-3" />
              <span>Add to Feed ({selectedItems.length})</span>
            </button>
            <button
              onClick={handleDeleteSelected}
              className="flex items-center space-x-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
            >
              <Trash2 className="h-3 w-3" />
              <span>Delete</span>
            </button>
            <button
              onClick={clearSelection}
              className="flex items-center space-x-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
            >
              <X className="h-3 w-3" />
              <span>Clear</span>
            </button>
          </div>
        )}
      </div>

      {/* Upload Area */}
      <div className="p-4 border-b border-gray-200">
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors
            ${isDragActive 
              ? 'border-primary-400 bg-primary-50' 
              : 'border-gray-300 hover:border-gray-400'
            }
          `}
        >
          <input {...getInputProps()} />
          <Upload className="h-6 w-6 text-gray-400 mx-auto mb-2" />
          <p className="text-sm font-medium text-gray-900">
            {isDragActive ? 'Drop images here' : 'Upload new images'}
          </p>
          <p className="text-xs text-gray-500">
            Drag & drop or click to select • JPEG, PNG, WebP up to 10MB
          </p>
          {uploading && (
            <div className="mt-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          )}
        </div>
      </div>

      {/* Media Grid */}
      <div className="flex-1 overflow-y-auto p-4">
        {filteredItems.length === 0 ? (
          <div className="text-center py-12">
            <ImageIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">
              {searchQuery ? 'No images found' : 'No images uploaded yet'}
            </p>
            <p className="text-sm text-gray-400">
              {searchQuery ? 'Try a different search term' : 'Upload your first image to get started'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            {filteredItems.map((item) => {
              const isSelected = selectedItems.includes(item.id)
              
              return (
                <div
                  key={item.id}
                  className={`
                    relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all
                    ${isSelected 
                      ? 'border-primary-500 ring-2 ring-primary-200' 
                      : 'border-transparent hover:border-gray-300'
                    }
                  `}
                  onClick={() => isSelected ? deselectItem(item.id) : selectItem(item.id)}
                >
                  {/* Image */}
                  <div className="aspect-square relative">
                    <Image
                      src={item.preview}
                      alt={item.name}
                      fill
                      className="object-cover"
                      unoptimized={item.preview.startsWith('data:')}
                    />
                    
                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center">
                        <Check className="h-3 w-3 text-white" />
                      </div>
                    )}

                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleAddToFeed(item)
                          }}
                          className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                          title="Add to feed"
                        >
                          <Plus className="h-4 w-4 text-gray-700" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            if (confirm('Delete this image?')) {
                              removeMediaItem(item.id)
                            }
                          }}
                          className="p-2 bg-red-500 bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                          title="Delete image"
                        >
                          <Trash2 className="h-4 w-4 text-white" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Image info */}
                  <div className="p-2 bg-white">
                    <p className="text-xs font-medium text-gray-900 truncate" title={item.name}>
                      {item.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {(item.size / 1024 / 1024).toFixed(1)} MB
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
