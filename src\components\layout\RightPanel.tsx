'use client'

import { useState } from 'react'
import { useFeed } from '@/contexts/FeedContext'
import { ImageUploader } from '@/components/upload/ImageUploader'
import { PostEditor } from '@/components/feed/PostEditor'
import { AIInsights } from '@/components/ai/AIInsights'
import { 
  Upload, 
  Edit3, 
  Sparkles, 
  Calendar,
  Share2,
  Save
} from 'lucide-react'

type TabType = 'upload' | 'edit' | 'ai' | 'schedule'

export function RightPanel() {
  const { selectedPost, posts } = useFeed()
  const [activeTab, setActiveTab] = useState<TabType>('upload')

  const tabs = [
    { id: 'upload', name: 'Upload', icon: Upload },
    { id: 'edit', name: 'Edit', icon: Edit3, disabled: !selectedPost },
    { id: 'ai', name: 'AI Insights', icon: Sparkles },
    { id: 'schedule', name: 'Schedule', icon: Calendar },
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            const isDisabled = tab.disabled
            
            return (
              <button
                key={tab.id}
                className={`
                  flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                  ${isActive 
                    ? 'border-primary-500 text-primary-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                  ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
                onClick={() => !isDisabled && setActiveTab(tab.id as TabType)}
                disabled={isDisabled}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'upload' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Upload Images
            </h3>
            <ImageUploader />
          </div>
        )}

        {activeTab === 'edit' && selectedPost && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Edit Post
            </h3>
            <PostEditor post={selectedPost} />
          </div>
        )}

        {activeTab === 'edit' && !selectedPost && (
          <div className="text-center py-8">
            <Edit3 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Select a post to edit</p>
          </div>
        )}

        {activeTab === 'ai' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              AI Insights
            </h3>
            <AIInsights posts={posts} />
          </div>
        )}

        {activeTab === 'schedule' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Schedule Posts
            </h3>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Quick Schedule</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Schedule all posts with optimal timing
                </p>
                <button className="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                  Auto Schedule
                </button>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Manual Schedule</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Choose specific dates and times
                </p>
                <button className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  Open Calendar
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="border-t border-gray-200 p-6">
        <div className="space-y-3">
          <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
            <Save className="h-4 w-4" />
            <span>Save Feed</span>
          </button>
          
          <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            <Share2 className="h-4 w-4" />
            <span>Share Preview</span>
          </button>
        </div>
      </div>
    </div>
  )
}
