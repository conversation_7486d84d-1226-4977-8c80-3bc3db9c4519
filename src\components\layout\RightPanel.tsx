'use client'

import { useState } from 'react'
import { useFeed } from '@/contexts/FeedContext'
import { ImageUploader } from '@/components/upload/ImageUploader'
import { PostEditor } from '@/components/feed/PostEditor'
import { AIInsights } from '@/components/ai/AIInsights'
import {
  Upload,
  Edit3,
  Sparkles,
  Calendar,
  Share2,
  Save
} from 'lucide-react'

type TabType = 'upload' | 'edit' | 'ai' | 'schedule'

export function RightPanel() {
  const { selectedPost, posts, saveFeed, saving, currentProject } = useFeed()
  const [activeTab, setActiveTab] = useState<TabType>('upload')
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [feedName, setFeedName] = useState('')

  const handleSave = async () => {
    if (posts.length === 0) {
      alert('Please add some images before saving!')
      return
    }

    if (!currentProject) {
      setShowSaveDialog(true)
    } else {
      await saveFeed()
      alert('Feed saved successfully!')
    }
  }

  const handleSaveWithName = async () => {
    if (!feedName.trim()) {
      alert('Please enter a name for your feed!')
      return
    }

    await saveFeed(feedName)
    setShowSaveDialog(false)
    setFeedName('')
    alert('Feed saved successfully!')
  }

  const tabs = [
    { id: 'upload', name: 'Upload', icon: Upload },
    { id: 'edit', name: 'Edit', icon: Edit3, disabled: !selectedPost },
    { id: 'ai', name: 'AI Insights', icon: Sparkles },
    { id: 'schedule', name: 'Schedule', icon: Calendar },
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            const isDisabled = tab.disabled

            return (
              <button
                key={tab.id}
                className={`
                  flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                  ${isActive
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                  ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
                onClick={() => !isDisabled && setActiveTab(tab.id as TabType)}
                disabled={isDisabled}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'upload' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Quick Upload
            </h3>
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">
                Use the <strong>Media Library</strong> for better image management!
              </p>
              <p className="text-sm text-gray-400">
                Click "Media Library" in the sidebar to upload, organize, and reuse your images.
              </p>
            </div>
          </div>
        )}

        {activeTab === 'edit' && selectedPost && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Edit Post
            </h3>
            <PostEditor post={selectedPost} />
          </div>
        )}

        {activeTab === 'edit' && !selectedPost && (
          <div className="text-center py-8">
            <Edit3 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Select a post to edit</p>
          </div>
        )}

        {activeTab === 'ai' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              AI Insights
            </h3>
            <AIInsights posts={posts} />
          </div>
        )}

        {activeTab === 'schedule' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Schedule Posts
            </h3>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Quick Schedule</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Schedule all posts with optimal timing
                </p>
                <button className="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                  Auto Schedule
                </button>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Manual Schedule</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Choose specific dates and times
                </p>
                <button className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  Open Calendar
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="border-t border-gray-200 p-6">
        <div className="space-y-3">
          <button
            onClick={handleSave}
            disabled={saving}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{saving ? 'Saving...' : currentProject ? 'Update Feed' : 'Save Feed'}</span>
          </button>

          <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            <Share2 className="h-4 w-4" />
            <span>Share Preview</span>
          </button>
        </div>
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setShowSaveDialog(false)} />

            <div className="relative bg-white rounded-xl shadow-xl max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Save Your Feed</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Feed Name
                  </label>
                  <input
                    type="text"
                    value={feedName}
                    onChange={(e) => setFeedName(e.target.value)}
                    placeholder="e.g., Summer Collection, Product Launch"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowSaveDialog(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveWithName}
                    disabled={saving}
                    className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
                  >
                    {saving ? 'Saving...' : 'Save'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
