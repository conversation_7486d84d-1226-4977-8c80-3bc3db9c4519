'use client'

import { useState } from 'react'
import { useFeed } from '@/contexts/FeedContext'
import { Post } from '@/lib/supabase'
import { 
  Type, 
  Hash, 
  Palette, 
  Crop, 
  RotateCw,
  Sun,
  Contrast,
  Zap
} from 'lucide-react'
import Image from 'next/image'

interface PostEditorProps {
  post: Post & { file?: File; preview?: string }
}

export function PostEditor({ post }: PostEditorProps) {
  const { updatePost } = useFeed()
  const [caption, setCaption] = useState(post.caption || '')
  const [hashtags, setHashtags] = useState(post.hashtags?.join(' ') || '')
  const [filters, setFilters] = useState({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
  })

  const handleSave = () => {
    updatePost(post.id, {
      caption,
      hashtags: hashtags.split(' ').filter(tag => tag.startsWith('#')),
    })
  }

  const suggestedHashtags = [
    '#photography', '#instagood', '#photooftheday', '#beautiful',
    '#picoftheday', '#instadaily', '#love', '#nature', '#art', '#style'
  ]

  return (
    <div className="space-y-6">
      {/* Image Preview */}
      <div className="relative aspect-square w-full max-w-xs mx-auto">
        {(post.preview || post.image_url) && (
          <Image
            src={post.preview || post.image_url}
            alt="Post preview"
            fill
            className="object-cover rounded-lg"
            style={{
              filter: `brightness(${filters.brightness}%) contrast(${filters.contrast}%) saturate(${filters.saturation}%) blur(${filters.blur}px)`
            }}
          />
        )}
      </div>

      {/* Image Filters */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 flex items-center">
          <Palette className="h-4 w-4 mr-2" />
          Filters & Adjustments
        </h4>
        
        <div className="space-y-3">
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <Sun className="h-4 w-4 mr-1" />
              Brightness
            </label>
            <input
              type="range"
              min="50"
              max="150"
              value={filters.brightness}
              onChange={(e) => setFilters(prev => ({ ...prev, brightness: Number(e.target.value) }))}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <Contrast className="h-4 w-4 mr-1" />
              Contrast
            </label>
            <input
              type="range"
              min="50"
              max="150"
              value={filters.contrast}
              onChange={(e) => setFilters(prev => ({ ...prev, contrast: Number(e.target.value) }))}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <Zap className="h-4 w-4 mr-1" />
              Saturation
            </label>
            <input
              type="range"
              min="0"
              max="200"
              value={filters.saturation}
              onChange={(e) => setFilters(prev => ({ ...prev, saturation: Number(e.target.value) }))}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Caption */}
      <div className="space-y-2">
        <label className="flex items-center text-sm font-medium text-gray-700">
          <Type className="h-4 w-4 mr-2" />
          Caption
        </label>
        <textarea
          value={caption}
          onChange={(e) => setCaption(e.target.value)}
          placeholder="Write a caption..."
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          rows={3}
        />
        <div className="text-xs text-gray-500">
          {caption.length}/2200 characters
        </div>
      </div>

      {/* Hashtags */}
      <div className="space-y-2">
        <label className="flex items-center text-sm font-medium text-gray-700">
          <Hash className="h-4 w-4 mr-2" />
          Hashtags
        </label>
        <textarea
          value={hashtags}
          onChange={(e) => setHashtags(e.target.value)}
          placeholder="#hashtag #another"
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          rows={2}
        />
        
        {/* Suggested Hashtags */}
        <div className="space-y-2">
          <p className="text-xs text-gray-500">Suggested hashtags:</p>
          <div className="flex flex-wrap gap-1">
            {suggestedHashtags.map((tag) => (
              <button
                key={tag}
                onClick={() => {
                  if (!hashtags.includes(tag)) {
                    setHashtags(prev => prev ? `${prev} ${tag}` : tag)
                  }
                }}
                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Save Button */}
      <button
        onClick={handleSave}
        className="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
      >
        Save Changes
      </button>
    </div>
  )
}
