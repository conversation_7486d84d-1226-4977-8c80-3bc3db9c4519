{"version": 3, "sources": ["../../../src/lib/memory/trace.ts"], "names": ["getAllMemoryUsageSpans", "startPeriodicMemoryUsageTracing", "stopPeriodicMemoryUsageTracing", "traceMemoryUsage", "HEAP_SNAPSHOT_THRESHOLD_PERCENT", "alreadyGeneratedHeapSnapshot", "TRACE_MEMORY_USAGE_TIMER_MS", "traceMemoryUsageTimer", "allMemoryUsage", "setTimeout", "clearTimeout", "description", "parentSpan", "memoryUsage", "process", "v8HeapStatistics", "v8", "getHeapStatistics", "heapUsed", "used_heap_size", "heapMax", "heap_size_limit", "tracedMemoryUsage", "rss", "heapTotal", "push", "tracedMemoryUsageAsStrings", "Object", "fromEntries", "entries", "map", "key", "value", "String", "<PERSON><PERSON><PERSON><PERSON>", "trace", "undefined", "env", "EXPERIMENTAL_DEBUG_MEMORY_USAGE", "percentageHeapUsed", "info", "toFixed", "distDir", "traceGlobals", "get", "heapFilename", "join", "replace", "warn", "bold", "italic", "writeHeapSnapshot"], "mappings": ";;;;;;;;;;;;;;;;;IA0CgBA,sBAAsB;eAAtBA;;IAhBAC,+BAA+B;eAA/BA;;IAOAC,8BAA8B;eAA9BA;;IAiBAC,gBAAgB;eAAhBA;;;2DAlDD;qBACY;uBACM;4BACJ;sBACR;wBACQ;;;;;;AAE7B,MAAMC,kCAAkC;AACxC,IAAIC,+BAA+B;AAEnC,MAAMC,8BAA8B;AACpC,IAAIC;AASJ,MAAMC,iBAAgC,EAAE;AAMjC,SAASP;IACdM,wBAAwBE,WAAW;QACjCN,iBAAiB;QACjBF;IACF,GAAGK;AACL;AAEO,SAASJ;IACd,IAAIK,uBAAuB;QACzBG,aAAaH;IACf;AACF;AAKO,SAASP;IACd,OAAOQ;AACT;AAMO,SAASL,iBACdQ,WAAmB,EACnBC,UAA6B;IAE7B,MAAMC,cAAcC,QAAQD,WAAW;IACvC,MAAME,mBAAmBC,WAAE,CAACC,iBAAiB;IAC7C,MAAMC,WAAWH,iBAAiBI,cAAc;IAChD,MAAMC,UAAUL,iBAAiBM,eAAe;IAChD,MAAMC,oBAAiC;QACrC,cAAcT,YAAYU,GAAG;QAC7B,mBAAmBL;QACnB,oBAAoBL,YAAYW,SAAS;QACzC,kBAAkBJ;IACpB;IACAZ,eAAeiB,IAAI,CAACH;IACpB,MAAMI,6BAA6BC,OAAOC,WAAW,CACnDD,OAAOE,OAAO,CAACP,mBAAmBQ,GAAG,CAAC,CAAC,CAACC,KAAKC,MAAM,GAAK;YACtDD;YACAE,OAAOD;SACR;IAEH,IAAIpB,YAAY;QACdA,WAAWsB,UAAU,CAAC,gBAAgBR;IACxC,OAAO;QACLS,IAAAA,YAAK,EAAC,gBAAgBC,WAAWV;IACnC;IACA,IAAIZ,QAAQuB,GAAG,CAACC,+BAA+B,EAAE;QAC/C,MAAMC,qBAAqB,AAAC,MAAMrB,WAAYE;QAE9CoB,IAAAA,SAAI,EAAC;QACLA,IAAAA,SAAI,EAAC;QACLA,IAAAA,SAAI,EAAC,CAAC,wBAAwB,EAAE7B,YAAY,EAAE,CAAC;QAC/C6B,IAAAA,SAAI,EAAC,CAAC,QAAQ,EAAE,AAAC3B,CAAAA,YAAYU,GAAG,GAAG,OAAO,IAAG,EAAGkB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC/DD,IAAAA,SAAI,EAAC,CAAC,cAAc,EAAE,AAACtB,CAAAA,WAAW,OAAO,IAAG,EAAGuB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC9DD,IAAAA,SAAI,EACF,CAAC,yBAAyB,EAAE,AAAC3B,CAAAA,YAAYW,SAAS,GAAG,OAAO,IAAG,EAAGiB,OAAO,CACvE,GACA,GAAG,CAAC;QAERD,IAAAA,SAAI,EAAC,CAAC,aAAa,EAAE,AAACpB,CAAAA,UAAU,OAAO,IAAG,EAAGqB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC5DD,IAAAA,SAAI,EAAC,CAAC,yBAAyB,EAAED,mBAAmBE,OAAO,CAAC,GAAG,CAAC,CAAC;QACjED,IAAAA,SAAI,EAAC;QACLA,IAAAA,SAAI,EAAC;QAEL,IAAID,qBAAqBnC,iCAAiC;YACxD,MAAMsC,UAAUC,oBAAY,CAACC,GAAG,CAAC;YACjC,MAAMC,eAAeC,IAAAA,UAAI,EACvBJ,SACA,CAAC,EAAE/B,YAAYoC,OAAO,CAAC,KAAK,KAAK,aAAa,CAAC;YAEjDC,IAAAA,SAAI,EACFC,IAAAA,gBAAI,EACF,CAAC,kCAAkC,EAAEV,mBAAmBE,OAAO,CAC7D,GACA,wBAAwB,CAAC;YAG/B,IAAI,CAACpC,8BAA8B;gBACjC2C,IAAAA,SAAI,EACFC,IAAAA,gBAAI,EACF,CAAC,wBAAwB,EAAEJ,aAAa,GAAG,EAAEK,IAAAA,kBAAM,EACjD,mCACA,CAAC;gBAGPlC,WAAE,CAACmC,iBAAiB,CAACN;gBACrBxC,+BAA+B;YACjC,OAAO;gBACL2C,IAAAA,SAAI,EACF;YAEJ;QACF;IACF;AACF"}