{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "names": ["BAILOUT_TO_CSR", "BailoutToCSRError", "Error", "constructor", "reason", "digest", "isBailoutToCSRError", "err"], "mappings": "AAAA,+GAA+G;AAC/G,MAAMA,iBAAiB;AAEvB,sFAAsF,GACtF,OAAO,MAAMC,0BAA0BC;IAGrCC,YAAY,AAAgBC,MAAc,CAAE;QAC1C,KAAK,CAAC,AAAC,wCAAqCA;aADlBA,SAAAA;aAFZC,SAASL;IAIzB;AACF;AAEA,4GAA4G,GAC5G,OAAO,SAASM,oBAAoBC,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIF,MAAM,KAAKL;AACxB"}