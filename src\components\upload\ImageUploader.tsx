'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { useFeed } from '@/contexts/FeedContext'
import { uploadImage } from '@/lib/supabase'
import {
  Upload,
  Image as ImageIcon,
  X,
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react'
import Image from 'next/image'
import { v4 as uuidv4 } from 'uuid'

interface UploadedFile {
  id: string
  file: File
  preview: string
  uploading: boolean
  uploaded: boolean
  error?: string
}

export function ImageUploader() {
  const { addPost } = useFeed()
  const [files, setFiles] = useState<UploadedFile[]>([])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const preview = e.target?.result as string
        const newFile = {
          id: uuidv4(),
          file,
          preview,
          uploading: false,
          uploaded: false,
        }
        setFiles(prev => [...prev, newFile])
      }
      reader.readAsDataURL(file)
    })
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.heic']
    },
    multiple: true,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id)
      // Don't revoke the blob URL immediately as it might be used in the feed
      // if (file) {
      //   URL.revokeObjectURL(file.preview)
      // }
      return prev.filter(f => f.id !== id)
    })
  }

  const uploadFile = async (fileData: UploadedFile) => {
    setFiles(prev => prev.map(f =>
      f.id === fileData.id ? { ...f, uploading: true, error: undefined } : f
    ))

    try {
      let imageUrl = fileData.preview // Fallback to local preview

      // Try to upload to Supabase, but don't fail if it doesn't work
      try {
        const { data, error } = await uploadImage(fileData.file)
        if (data?.publicUrl && !error) {
          imageUrl = data.publicUrl
        }
      } catch (uploadError) {
        console.log('Supabase upload failed, using local preview:', uploadError)
      }

      // Add to feed (always works, even if upload fails)
      const newPost = {
        id: uuidv4(),
        user_id: '', // Will be set by context
        image_url: imageUrl,
        preview: fileData.preview,
        file: fileData.file,
        caption: '',
        hashtags: [],
        position: 0,
      }

      // console.log('Adding new post:', newPost)
      addPost(newPost)

      setFiles(prev => prev.map(f =>
        f.id === fileData.id ? { ...f, uploading: false, uploaded: true } : f
      ))

      // Remove after 2 seconds
      setTimeout(() => removeFile(fileData.id), 2000)

    } catch (error) {
      setFiles(prev => prev.map(f =>
        f.id === fileData.id ? {
          ...f,
          uploading: false,
          error: error instanceof Error ? error.message : 'Upload failed'
        } : f
      ))
    }
  }

  const uploadAll = () => {
    files.filter(f => !f.uploaded && !f.uploading).forEach(uploadFile)
  }

  return (
    <div className="space-y-4">
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragActive
            ? 'border-primary-400 bg-primary-50'
            : 'border-gray-300 hover:border-gray-400'
          }
        `}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          <Upload className="h-8 w-8 text-gray-400 mx-auto" />
          <div>
            <p className="text-sm font-medium text-gray-900">
              {isDragActive ? 'Drop images here' : 'Upload images'}
            </p>
            <p className="text-xs text-gray-500">
              Drag & drop or click to select • JPEG, PNG, WebP up to 10MB
            </p>
          </div>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Uploaded Files ({files.length})
            </h4>
            {files.some(f => !f.uploaded && !f.uploading) && (
              <button
                onClick={uploadAll}
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Upload All
              </button>
            )}
          </div>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            {files.map((fileData) => (
              <div
                key={fileData.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
              >
                {/* Preview */}
                <div className="relative w-12 h-12 flex-shrink-0">
                  <Image
                    src={fileData.preview}
                    alt="Preview"
                    fill
                    className="object-cover rounded"
                  />
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {fileData.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(fileData.file.size / 1024 / 1024).toFixed(1)} MB
                  </p>
                  {fileData.error && (
                    <p className="text-xs text-red-600 flex items-center mt-1">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {fileData.error}
                    </p>
                  )}
                </div>

                {/* Status */}
                <div className="flex items-center space-x-2">
                  {fileData.uploading && (
                    <Loader2 className="h-4 w-4 text-primary-600 animate-spin" />
                  )}
                  {fileData.uploaded && (
                    <Check className="h-4 w-4 text-green-600" />
                  )}
                  {!fileData.uploaded && !fileData.uploading && (
                    <button
                      onClick={() => uploadFile(fileData)}
                      className="text-xs px-2 py-1 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                    >
                      Upload
                    </button>
                  )}
                  <button
                    onClick={() => removeFile(fileData.id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          📸 Upload Tips
        </h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Square images (1:1) work best for Instagram</li>
          <li>• High resolution images (1080x1080px or higher)</li>
          <li>• Keep file sizes under 10MB for faster uploads</li>
          <li>• Upload multiple images to plan your entire feed</li>
        </ul>
      </div>
    </div>
  )
}
