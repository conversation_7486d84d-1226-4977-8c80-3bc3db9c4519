'use client'

import { useState, useEffect } from 'react'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { Header } from '@/components/layout/Header'
import { Sidebar } from '@/components/layout/Sidebar'
import { FeedGrid } from '@/components/feed/FeedGrid'
import { RightPanel } from '@/components/layout/RightPanel'
import { MediaLibrary } from '@/components/media/MediaLibrary'
import { useAuth } from '@/hooks/useAuth'
import { AuthModal } from '@/components/auth/AuthModal'
import { LandingPage } from '@/components/landing/LandingPage'

export default function Home() {
  const { user, loading } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [currentView, setCurrentView] = useState<'feed' | 'media'>('feed')

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <>
        <LandingPage onGetStarted={() => setShowAuthModal(true)} />
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </>
    )
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex">
          <Sidebar onNavigate={setCurrentView} currentView={currentView} />
          <main className="flex-1 p-6">
            <div className="max-w-7xl mx-auto">
              {currentView === 'feed' ? (
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  <div className="lg:col-span-3">
                    <div className="bg-white rounded-xl shadow-sm p-6">
                      <div className="flex items-center justify-between mb-6">
                        <h1 className="text-2xl font-bold text-gray-900">
                          Feed Preview
                        </h1>
                        <div className="flex items-center space-x-2">
                          <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            3x3 Grid
                          </button>
                          <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            6x6 Grid
                          </button>
                        </div>
                      </div>
                      <FeedGrid />
                    </div>
                  </div>
                  <div className="lg:col-span-1">
                    <RightPanel />
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-xl shadow-sm h-[calc(100vh-8rem)]">
                  <MediaLibrary />
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </DndProvider>
  )
}
