{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "INSTAGRAM_CLIENT_ID": "@instagram_client_id", "INSTAGRAM_CLIENT_SECRET": "@instagram_client_secret", "STRIPE_PUBLISHABLE_KEY": "@stripe_publishable_key", "STRIPE_SECRET_KEY": "@stripe_secret_key"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}