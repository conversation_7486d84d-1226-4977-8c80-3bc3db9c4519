'use client'

import {
  Grid3X3,
  Sparkles,
  TrendingUp,
  Calendar,
  Palette,
  Target,
  Check,
  Star,
  ArrowRight,
  Play
} from 'lucide-react'

interface LandingPageProps {
  onGetStarted: () => void
}

export function LandingPage({ onGetStarted }: LandingPageProps) {
  const features = [
    {
      icon: Grid3X3,
      title: 'Visual Feed Planning',
      description: 'Drag and drop to arrange your Instagram posts in a beautiful 3x3 or 6x6 grid layout.'
    },
    {
      icon: Spark<PERSON>,
      title: 'AI-Powered Insights',
      description: 'Get intelligent suggestions for color harmony, engagement prediction, and aesthetic scoring.'
    },
    {
      icon: Calendar,
      title: 'Smart Scheduling',
      description: 'Schedule posts at optimal times with our AI-driven timing recommendations.'
    },
    {
      icon: Palette,
      title: 'Color Analysis',
      description: 'Analyze your feed\'s color palette and get suggestions for better visual consistency.'
    },
    {
      icon: TrendingUp,
      title: 'Engagement Prediction',
      description: 'Predict how well your posts will perform before you publish them.'
    },
    {
      icon: Target,
      title: 'Template Library',
      description: 'Choose from professionally designed templates for different industries and aesthetics.'
    }
  ]

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Fashion Influencer',
      content: 'VisualVibe transformed my Instagram strategy. My engagement increased by 150% in just 2 months!',
      rating: 5,
      avatar: '👩‍💼'
    },
    {
      name: '<PERSON>',
      role: 'Food Blogger',
      content: 'The AI insights are incredible. It helps me maintain a cohesive aesthetic that my followers love.',
      rating: 5,
      avatar: '👨‍🍳'
    },
    {
      name: 'Emma Davis',
      role: 'Travel Photographer',
      content: 'Planning my feed has never been easier. The drag-and-drop interface is so intuitive!',
      rating: 5,
      avatar: '📸'
    }
  ]

  const pricingPlans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      features: [
        '3 saved feed layouts',
        'Basic drag-and-drop',
        'Upload 20 images/month',
        'Basic templates',
        'Community support'
      ],
      cta: 'Get Started Free',
      popular: false
    },
    {
      name: 'Premium',
      price: '$9',
      period: 'month',
      features: [
        'Unlimited saved layouts',
        'AI optimization features',
        'Advanced analytics',
        'Auto-posting',
        'Premium templates',
        'Priority support'
      ],
      cta: 'Start Free Trial',
      popular: true
    },
    {
      name: 'Pro',
      price: '$29',
      period: 'month',
      features: [
        'Multiple Instagram accounts',
        'Team collaboration',
        'Advanced scheduling',
        'Custom branding',
        'API access',
        'Dedicated support'
      ],
      cta: 'Contact Sales',
      popular: false
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="instagram-gradient w-8 h-8 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">VV</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">VisualVibe</span>
            </div>
            <button
              onClick={onGetStarted}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Sign In
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Plan Your Perfect
              <span className="instagram-gradient bg-clip-text text-transparent"> Instagram Feed</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Create stunning, cohesive Instagram feeds with AI-powered insights,
              drag-and-drop planning, and smart scheduling. Boost your engagement
              and grow your following with VisualVibe.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={onGetStarted}
                className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center"
              >
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
              <button className="px-8 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-gray-600">
              Powerful tools to plan, optimize, and schedule your Instagram content
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="text-center p-6">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Loved by Creators Worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our users are saying about VisualVibe
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4">&ldquo;{testimonial.content}&rdquo;</p>
                <div className="flex items-center">
                  <span className="text-2xl mr-3">{testimonial.avatar}</span>
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.name}</p>
                    <p className="text-sm text-gray-500">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600">
              Choose the plan that&apos;s right for you
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <div
                key={index}
                className={`
                  relative bg-white rounded-xl border-2 p-8
                  ${plan.popular ? 'border-primary-500 shadow-lg' : 'border-gray-200'}
                `}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-500 ml-1">/{plan.period}</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-3" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={onGetStarted}
                  className={`
                    w-full py-3 px-4 rounded-lg font-medium transition-colors
                    ${plan.popular
                      ? 'bg-primary-600 text-white hover:bg-primary-700'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                    }
                  `}
                >
                  {plan.cta}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Transform Your Instagram?
          </h2>
          <p className="text-xl text-purple-100 mb-8">
            Join thousands of creators who are already using VisualVibe to grow their following
          </p>
          <button
            onClick={onGetStarted}
            className="px-8 py-3 bg-white text-purple-600 rounded-lg hover:bg-gray-100 transition-colors font-medium"
          >
            Start Your Free Trial Today
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="instagram-gradient w-8 h-8 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">VV</span>
              </div>
              <span className="ml-2 text-xl font-bold">VisualVibe</span>
            </div>
            <p className="text-gray-400">
              © 2024 VisualVibe. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
