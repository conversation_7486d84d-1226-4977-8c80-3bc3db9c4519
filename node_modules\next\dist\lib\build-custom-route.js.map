{"version": 3, "sources": ["../../src/lib/build-custom-route.ts"], "names": ["buildCustomRoute", "type", "route", "restrictedRedirectPaths", "compiled", "pathToRegexp", "source", "strict", "sensitive", "delimiter", "internal", "modifyRouteRegex", "undefined", "regex", "normalizeRouteRegex", "statusCode", "getRedirectStatus", "permanent"], "mappings": ";;;;+BA4BgBA;;;eAAAA;;;8BA5Ba;kCAYtB;gCAC6C;AAe7C,SAASA,iBACdC,IAAe,EACfC,KAAkC,EAClCC,uBAAkC;IAElC,MAAMC,WAAWC,IAAAA,0BAAY,EAACH,MAAMI,MAAM,EAAE,EAAE,EAAE;QAC9CC,QAAQ;QACRC,WAAW;QACXC,WAAW;IACb;IAEA,IAAIH,SAASF,SAASE,MAAM;IAC5B,IAAI,CAACJ,MAAMQ,QAAQ,EAAE;QACnBJ,SAASK,IAAAA,gCAAgB,EACvBL,QACAL,SAAS,aAAaE,0BAA0BS;IAEpD;IAEA,MAAMC,QAAQC,IAAAA,qCAAmB,EAACR;IAElC,IAAIL,SAAS,YAAY;QACvB,OAAO;YAAE,GAAGC,KAAK;YAAEW;QAAM;IAC3B;IAEA,OAAO;QACL,GAAGX,KAAK;QACRa,YAAYC,IAAAA,iCAAiB,EAACd;QAC9Be,WAAWL;QACXC;IACF;AACF"}