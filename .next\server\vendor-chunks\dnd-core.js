"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dnd-core";
exports.ids = ["vendor-chunks/dnd-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBeginDrag: () => (/* binding */ createBeginDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./local/setClientOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\nconst ResetCoordinatesAction = {\n    type: _types_js__WEBPACK_IMPORTED_MODULE_1__.INIT_COORDS,\n    payload: {\n        clientOffset: null,\n        sourceClientOffset: null\n    }\n};\nfunction createBeginDrag(manager) {\n    return function beginDrag(sourceIds = [], options = {\n        publishSource: true\n    }) {\n        const { publishSource =true , clientOffset , getSourceClientOffset ,  } = options;\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        // Initialize the coordinates using the client offset\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset));\n        verifyInvariants(sourceIds, monitor, registry);\n        // Get the draggable source\n        const sourceId = getDraggableSource(sourceIds, monitor);\n        if (sourceId == null) {\n            manager.dispatch(ResetCoordinatesAction);\n            return;\n        }\n        // Get the source client offset\n        let sourceClientOffset = null;\n        if (clientOffset) {\n            if (!getSourceClientOffset) {\n                throw new Error('getSourceClientOffset must be defined');\n            }\n            verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n            sourceClientOffset = getSourceClientOffset(sourceId);\n        }\n        // Initialize the full coordinates\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset, sourceClientOffset));\n        const source = registry.getSource(sourceId);\n        const item = source.beginDrag(monitor, sourceId);\n        // If source.beginDrag returns null, this is an indicator to cancel the drag\n        if (item == null) {\n            return undefined;\n        }\n        verifyItemIsObject(item);\n        registry.pinSource(sourceId);\n        const itemType = registry.getSourceType(sourceId);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG,\n            payload: {\n                itemType,\n                item,\n                sourceId,\n                clientOffset: clientOffset || null,\n                sourceClientOffset: sourceClientOffset || null,\n                isSourcePublic: !!publishSource\n            }\n        };\n    };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.isDragging(), 'Cannot call beginDrag while dragging.');\n    sourceIds.forEach(function(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(registry.getSource(sourceId), 'Expected sourceIds to be registered.');\n    });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof getSourceClientOffset === 'function', 'When clientOffset is provided, getSourceClientOffset must be a function.');\n}\nfunction verifyItemIsObject(item) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)((0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.isObject)(item), 'Item must be an object.');\n}\nfunction getDraggableSource(sourceIds, monitor) {\n    let sourceId = null;\n    for(let i = sourceIds.length - 1; i >= 0; i--){\n        if (monitor.canDragSource(sourceIds[i])) {\n            sourceId = sourceIds[i];\n            break;\n        }\n    }\n    return sourceId;\n}\n\n//# sourceMappingURL=beginDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/drop.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDrop: () => (/* binding */ createDrop)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nfunction createDrop(manager) {\n    return function drop(options = {}) {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyInvariants(monitor);\n        const targetIds = getDroppableTargets(monitor);\n        // Multiple actions are dispatched here, which is why this doesn't return an action\n        targetIds.forEach((targetId, index)=>{\n            const dropResult = determineDropResult(targetId, index, registry, monitor);\n            const action = {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_1__.DROP,\n                payload: {\n                    dropResult: _objectSpread({}, options, dropResult)\n                }\n            };\n            manager.dispatch(action);\n        });\n    };\n}\nfunction verifyInvariants(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call drop while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n    const target = registry.getTarget(targetId);\n    let dropResult = target ? target.drop(monitor, targetId) : undefined;\n    verifyDropResultType(dropResult);\n    if (typeof dropResult === 'undefined') {\n        dropResult = index === 0 ? {} : monitor.getDropResult();\n    }\n    return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof dropResult === 'undefined' || (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n    const targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n    targetIds.reverse();\n    return targetIds;\n}\n\n//# sourceMappingURL=drop.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEndDrag: () => (/* binding */ createEndDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\nfunction createEndDrag(manager) {\n    return function endDrag() {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyIsDragging(monitor);\n        const sourceId = monitor.getSourceId();\n        if (sourceId != null) {\n            const source = registry.getSource(sourceId, true);\n            source.endDrag(monitor, sourceId);\n            registry.unpinSource();\n        }\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG\n        };\n    };\n}\nfunction verifyIsDragging(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call endDrag while not dragging.');\n}\n\n//# sourceMappingURL=endDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2VuZERyYWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlEO0FBQ1g7QUFDL0I7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLCtDQUFRO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrREFBUztBQUNiOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2VuZERyYWcuanM/YTM4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICdAcmVhY3QtZG5kL2ludmFyaWFudCc7XG5pbXBvcnQgeyBFTkRfRFJBRyB9IGZyb20gJy4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUVuZERyYWcobWFuYWdlcikge1xuICAgIHJldHVybiBmdW5jdGlvbiBlbmREcmFnKCkge1xuICAgICAgICBjb25zdCBtb25pdG9yID0gbWFuYWdlci5nZXRNb25pdG9yKCk7XG4gICAgICAgIGNvbnN0IHJlZ2lzdHJ5ID0gbWFuYWdlci5nZXRSZWdpc3RyeSgpO1xuICAgICAgICB2ZXJpZnlJc0RyYWdnaW5nKG1vbml0b3IpO1xuICAgICAgICBjb25zdCBzb3VyY2VJZCA9IG1vbml0b3IuZ2V0U291cmNlSWQoKTtcbiAgICAgICAgaWYgKHNvdXJjZUlkICE9IG51bGwpIHtcbiAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IHJlZ2lzdHJ5LmdldFNvdXJjZShzb3VyY2VJZCwgdHJ1ZSk7XG4gICAgICAgICAgICBzb3VyY2UuZW5kRHJhZyhtb25pdG9yLCBzb3VyY2VJZCk7XG4gICAgICAgICAgICByZWdpc3RyeS51bnBpblNvdXJjZSgpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlOiBFTkRfRFJBR1xuICAgICAgICB9O1xuICAgIH07XG59XG5mdW5jdGlvbiB2ZXJpZnlJc0RyYWdnaW5nKG1vbml0b3IpIHtcbiAgICBpbnZhcmlhbnQobW9uaXRvci5pc0RyYWdnaW5nKCksICdDYW5ub3QgY2FsbCBlbmREcmFnIHdoaWxlIG5vdCBkcmFnZ2luZy4nKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW5kRHJhZy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/hover.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHover: () => (/* binding */ createHover)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\nfunction createHover(manager) {\n    return function hover(targetIdsArg, { clientOffset  } = {}) {\n        verifyTargetIdsIsArray(targetIdsArg);\n        const targetIds = targetIdsArg.slice(0);\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        const draggedItemType = monitor.getItemType();\n        removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n        checkInvariants(targetIds, monitor, registry);\n        hoverAllTargets(targetIds, monitor, registry);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.HOVER,\n            payload: {\n                targetIds,\n                clientOffset: clientOffset || null\n            }\n        };\n    };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call hover while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call hover after drop.');\n    for(let i = 0; i < targetIds.length; i++){\n        const targetId = targetIds[i];\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n        const target = registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, 'Expected targetIds to be registered.');\n    }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n    // Remove those targetIds that don't match the targetType.  This\n    // fixes shallow isOver which would only be non-shallow because of\n    // non-matching targets.\n    for(let i = targetIds.length - 1; i >= 0; i--){\n        const targetId = targetIds[i];\n        const targetType = registry.getTargetType(targetId);\n        if (!(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            targetIds.splice(i, 1);\n        }\n    }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n    // Finally call hover on all matching targets.\n    targetIds.forEach(function(targetId) {\n        const target = registry.getTarget(targetId);\n        target.hover(monitor, targetId);\n    });\n}\n\n//# sourceMappingURL=hover.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.DROP),\n/* harmony export */   END_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG),\n/* harmony export */   HOVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.HOVER),\n/* harmony export */   INIT_COORDS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE),\n/* harmony export */   createDragDropActions: () => (/* binding */ createDragDropActions)\n/* harmony export */ });\n/* harmony import */ var _beginDrag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./beginDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\");\n/* harmony import */ var _drop_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drop.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\");\n/* harmony import */ var _endDrag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./endDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\");\n/* harmony import */ var _hover_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hover.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\");\n/* harmony import */ var _publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./publishDragSource.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\n\n\nfunction createDragDropActions(manager) {\n    return {\n        beginDrag: (0,_beginDrag_js__WEBPACK_IMPORTED_MODULE_1__.createBeginDrag)(manager),\n        publishDragSource: (0,_publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__.createPublishDragSource)(manager),\n        hover: (0,_hover_js__WEBPACK_IMPORTED_MODULE_3__.createHover)(manager),\n        drop: (0,_drop_js__WEBPACK_IMPORTED_MODULE_4__.createDrop)(manager),\n        endDrag: (0,_endDrag_js__WEBPACK_IMPORTED_MODULE_5__.createEndDrag)(manager)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUQ7QUFDVjtBQUNNO0FBQ0o7QUFDd0I7QUFDdEM7QUFDcEI7QUFDUDtBQUNBLG1CQUFtQiw4REFBZTtBQUNsQywyQkFBMkIsOEVBQXVCO0FBQ2xELGVBQWUsc0RBQVc7QUFDMUIsY0FBYyxvREFBVTtBQUN4QixpQkFBaUIsMERBQWE7QUFDOUI7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvYWN0aW9ucy9kcmFnRHJvcC9pbmRleC5qcz8yNmMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJlZ2luRHJhZyB9IGZyb20gJy4vYmVnaW5EcmFnLmpzJztcbmltcG9ydCB7IGNyZWF0ZURyb3AgfSBmcm9tICcuL2Ryb3AuanMnO1xuaW1wb3J0IHsgY3JlYXRlRW5kRHJhZyB9IGZyb20gJy4vZW5kRHJhZy5qcyc7XG5pbXBvcnQgeyBjcmVhdGVIb3ZlciB9IGZyb20gJy4vaG92ZXIuanMnO1xuaW1wb3J0IHsgY3JlYXRlUHVibGlzaERyYWdTb3VyY2UgfSBmcm9tICcuL3B1Ymxpc2hEcmFnU291cmNlLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZURyYWdEcm9wQWN0aW9ucyhtYW5hZ2VyKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYmVnaW5EcmFnOiBjcmVhdGVCZWdpbkRyYWcobWFuYWdlciksXG4gICAgICAgIHB1Ymxpc2hEcmFnU291cmNlOiBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZShtYW5hZ2VyKSxcbiAgICAgICAgaG92ZXI6IGNyZWF0ZUhvdmVyKG1hbmFnZXIpLFxuICAgICAgICBkcm9wOiBjcmVhdGVEcm9wKG1hbmFnZXIpLFxuICAgICAgICBlbmREcmFnOiBjcmVhdGVFbmREcmFnKG1hbmFnZXIpXG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js":
/*!******************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setClientOffset: () => (/* binding */ setClientOffset)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction setClientOffset(clientOffset, sourceClientOffset) {\n    return {\n        type: _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS,\n        payload: {\n            sourceClientOffset: sourceClientOffset || null,\n            clientOffset: clientOffset || null\n        }\n    };\n}\n\n//# sourceMappingURL=setClientOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2xvY2FsL3NldENsaWVudE9mZnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUNuQztBQUNQO0FBQ0EsY0FBYyxrREFBVztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2FjdGlvbnMvZHJhZ0Ryb3AvbG9jYWwvc2V0Q2xpZW50T2Zmc2V0LmpzP2RhNGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSU5JVF9DT09SRFMgfSBmcm9tICcuLi90eXBlcy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gc2V0Q2xpZW50T2Zmc2V0KGNsaWVudE9mZnNldCwgc291cmNlQ2xpZW50T2Zmc2V0KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogSU5JVF9DT09SRFMsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHNvdXJjZUNsaWVudE9mZnNldDogc291cmNlQ2xpZW50T2Zmc2V0IHx8IG51bGwsXG4gICAgICAgICAgICBjbGllbnRPZmZzZXQ6IGNsaWVudE9mZnNldCB8fCBudWxsXG4gICAgICAgIH1cbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZXRDbGllbnRPZmZzZXQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js":
/*!**************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPublishDragSource: () => (/* binding */ createPublishDragSource)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction createPublishDragSource(manager) {\n    return function publishDragSource() {\n        const monitor = manager.getMonitor();\n        if (monitor.isDragging()) {\n            return {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE\n            };\n        }\n        return;\n    };\n}\n\n//# sourceMappingURL=publishDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3B1Ymxpc2hEcmFnU291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQzFDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2FjdGlvbnMvZHJhZ0Ryb3AvcHVibGlzaERyYWdTb3VyY2UuanM/ZGIxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQVUJMSVNIX0RSQUdfU09VUkNFIH0gZnJvbSAnLi90eXBlcy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlUHVibGlzaERyYWdTb3VyY2UobWFuYWdlcikge1xuICAgIHJldHVybiBmdW5jdGlvbiBwdWJsaXNoRHJhZ1NvdXJjZSgpIHtcbiAgICAgICAgY29uc3QgbW9uaXRvciA9IG1hbmFnZXIuZ2V0TW9uaXRvcigpO1xuICAgICAgICBpZiAobW9uaXRvci5pc0RyYWdnaW5nKCkpIHtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgdHlwZTogUFVCTElTSF9EUkFHX1NPVVJDRVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHVibGlzaERyYWdTb3VyY2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* binding */ BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* binding */ DROP),\n/* harmony export */   END_DRAG: () => (/* binding */ END_DRAG),\n/* harmony export */   HOVER: () => (/* binding */ HOVER),\n/* harmony export */   INIT_COORDS: () => (/* binding */ INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* binding */ PUBLISH_DRAG_SOURCE)\n/* harmony export */ });\nconst INIT_COORDS = 'dnd-core/INIT_COORDS';\nconst BEGIN_DRAG = 'dnd-core/BEGIN_DRAG';\nconst PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE';\nconst HOVER = 'dnd-core/HOVER';\nconst DROP = 'dnd-core/DROP';\nconst END_DRAG = 'dnd-core/END_DRAG';\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFUCIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvYWN0aW9ucy9kcmFnRHJvcC90eXBlcy5qcz9hYzI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBJTklUX0NPT1JEUyA9ICdkbmQtY29yZS9JTklUX0NPT1JEUyc7XG5leHBvcnQgY29uc3QgQkVHSU5fRFJBRyA9ICdkbmQtY29yZS9CRUdJTl9EUkFHJztcbmV4cG9ydCBjb25zdCBQVUJMSVNIX0RSQUdfU09VUkNFID0gJ2RuZC1jb3JlL1BVQkxJU0hfRFJBR19TT1VSQ0UnO1xuZXhwb3J0IGNvbnN0IEhPVkVSID0gJ2RuZC1jb3JlL0hPVkVSJztcbmV4cG9ydCBjb25zdCBEUk9QID0gJ2RuZC1jb3JlL0RST1AnO1xuZXhwb3J0IGNvbnN0IEVORF9EUkFHID0gJ2RuZC1jb3JlL0VORF9EUkFHJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/registry.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/registry.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_SOURCE: () => (/* binding */ ADD_SOURCE),\n/* harmony export */   ADD_TARGET: () => (/* binding */ ADD_TARGET),\n/* harmony export */   REMOVE_SOURCE: () => (/* binding */ REMOVE_SOURCE),\n/* harmony export */   REMOVE_TARGET: () => (/* binding */ REMOVE_TARGET),\n/* harmony export */   addSource: () => (/* binding */ addSource),\n/* harmony export */   addTarget: () => (/* binding */ addTarget),\n/* harmony export */   removeSource: () => (/* binding */ removeSource),\n/* harmony export */   removeTarget: () => (/* binding */ removeTarget)\n/* harmony export */ });\nconst ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nconst ADD_TARGET = 'dnd-core/ADD_TARGET';\nconst REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nconst REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nfunction addSource(sourceId) {\n    return {\n        type: ADD_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction addTarget(targetId) {\n    return {\n        type: ADD_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\nfunction removeSource(sourceId) {\n    return {\n        type: REMOVE_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction removeTarget(targetId) {\n    return {\n        type: REMOVE_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\n\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL3JlZ2lzdHJ5LmpzPzFmN2IiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEFERF9TT1VSQ0UgPSAnZG5kLWNvcmUvQUREX1NPVVJDRSc7XG5leHBvcnQgY29uc3QgQUREX1RBUkdFVCA9ICdkbmQtY29yZS9BRERfVEFSR0VUJztcbmV4cG9ydCBjb25zdCBSRU1PVkVfU09VUkNFID0gJ2RuZC1jb3JlL1JFTU9WRV9TT1VSQ0UnO1xuZXhwb3J0IGNvbnN0IFJFTU9WRV9UQVJHRVQgPSAnZG5kLWNvcmUvUkVNT1ZFX1RBUkdFVCc7XG5leHBvcnQgZnVuY3Rpb24gYWRkU291cmNlKHNvdXJjZUlkKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogQUREX1NPVVJDRSxcbiAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgc291cmNlSWRcbiAgICAgICAgfVxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gYWRkVGFyZ2V0KHRhcmdldElkKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogQUREX1RBUkdFVCxcbiAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgdGFyZ2V0SWRcbiAgICAgICAgfVxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gcmVtb3ZlU291cmNlKHNvdXJjZUlkKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogUkVNT1ZFX1NPVVJDRSxcbiAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgc291cmNlSWRcbiAgICAgICAgfVxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gcmVtb3ZlVGFyZ2V0KHRhcmdldElkKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogUkVNT1ZFX1RBUkdFVCxcbiAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgdGFyZ2V0SWRcbiAgICAgICAgfVxuICAgIH07XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZ2lzdHJ5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropManagerImpl: () => (/* binding */ DragDropManagerImpl)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\");\n\nclass DragDropManagerImpl {\n    receiveBackend(backend) {\n        this.backend = backend;\n    }\n    getMonitor() {\n        return this.monitor;\n    }\n    getBackend() {\n        return this.backend;\n    }\n    getRegistry() {\n        return this.monitor.registry;\n    }\n    getActions() {\n        /* eslint-disable-next-line @typescript-eslint/no-this-alias */ const manager = this;\n        const { dispatch  } = this.store;\n        function bindActionCreator(actionCreator) {\n            return (...args)=>{\n                const action = actionCreator.apply(manager, args);\n                if (typeof action !== 'undefined') {\n                    dispatch(action);\n                }\n            };\n        }\n        const actions = (0,_actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.createDragDropActions)(this);\n        return Object.keys(actions).reduce((boundActions, key)=>{\n            const action = actions[key];\n            boundActions[key] = bindActionCreator(action);\n            return boundActions;\n        }, {});\n    }\n    dispatch(action) {\n        this.store.dispatch(action);\n    }\n    constructor(store, monitor){\n        this.isSetUp = false;\n        this.handleRefCountChange = ()=>{\n            const shouldSetUp = this.store.getState().refCount > 0;\n            if (this.backend) {\n                if (shouldSetUp && !this.isSetUp) {\n                    this.backend.setup();\n                    this.isSetUp = true;\n                } else if (!shouldSetUp && this.isSetUp) {\n                    this.backend.teardown();\n                    this.isSetUp = false;\n                }\n            }\n        };\n        this.store = store;\n        this.monitor = monitor;\n        store.subscribe(this.handleRefCountChange);\n    }\n}\n\n//# sourceMappingURL=DragDropManagerImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropMonitorImpl: () => (/* binding */ DragDropMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_coords_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/coords.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/coords.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n\n\n\n\nclass DragDropMonitorImpl {\n    subscribeToStateChange(listener, options = {}) {\n        const { handlerIds  } = options;\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof handlerIds === 'undefined' || Array.isArray(handlerIds), 'handlerIds, when specified, must be an array of strings.');\n        let prevStateId = this.store.getState().stateId;\n        const handleChange = ()=>{\n            const state = this.store.getState();\n            const currentStateId = state.stateId;\n            try {\n                const canSkipListener = currentStateId === prevStateId || currentStateId === prevStateId + 1 && !(0,_utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__.areDirty)(state.dirtyHandlerIds, handlerIds);\n                if (!canSkipListener) {\n                    listener();\n                }\n            } finally{\n                prevStateId = currentStateId;\n            }\n        };\n        return this.store.subscribe(handleChange);\n    }\n    subscribeToOffsetChange(listener) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        let previousState = this.store.getState().dragOffset;\n        const handleChange = ()=>{\n            const nextState = this.store.getState().dragOffset;\n            if (nextState === previousState) {\n                return;\n            }\n            previousState = nextState;\n            listener();\n        };\n        return this.store.subscribe(handleChange);\n    }\n    canDragSource(sourceId) {\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (this.isDragging()) {\n            return false;\n        }\n        return source.canDrag(this, sourceId);\n    }\n    canDropOnTarget(targetId) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const target = this.registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, `Expected to find a valid target. targetId=${targetId}`);\n        if (!this.isDragging() || this.didDrop()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        return (0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType) && target.canDrop(this, targetId);\n    }\n    isDragging() {\n        return Boolean(this.getItemType());\n    }\n    isDraggingSource(sourceId) {\n        // undefined on initial render\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId, true);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (!this.isDragging() || !this.isSourcePublic()) {\n            return false;\n        }\n        const sourceType = this.registry.getSourceType(sourceId);\n        const draggedItemType = this.getItemType();\n        if (sourceType !== draggedItemType) {\n            return false;\n        }\n        return source.isDragging(this, sourceId);\n    }\n    isOverTarget(targetId, options = {\n        shallow: false\n    }) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const { shallow  } = options;\n        if (!this.isDragging()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        if (draggedItemType && !(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            return false;\n        }\n        const targetIds = this.getTargetIds();\n        if (!targetIds.length) {\n            return false;\n        }\n        const index = targetIds.indexOf(targetId);\n        if (shallow) {\n            return index === targetIds.length - 1;\n        } else {\n            return index > -1;\n        }\n    }\n    getItemType() {\n        return this.store.getState().dragOperation.itemType;\n    }\n    getItem() {\n        return this.store.getState().dragOperation.item;\n    }\n    getSourceId() {\n        return this.store.getState().dragOperation.sourceId;\n    }\n    getTargetIds() {\n        return this.store.getState().dragOperation.targetIds;\n    }\n    getDropResult() {\n        return this.store.getState().dragOperation.dropResult;\n    }\n    didDrop() {\n        return this.store.getState().dragOperation.didDrop;\n    }\n    isSourcePublic() {\n        return Boolean(this.store.getState().dragOperation.isSourcePublic);\n    }\n    getInitialClientOffset() {\n        return this.store.getState().dragOffset.initialClientOffset;\n    }\n    getInitialSourceClientOffset() {\n        return this.store.getState().dragOffset.initialSourceClientOffset;\n    }\n    getClientOffset() {\n        return this.store.getState().dragOffset.clientOffset;\n    }\n    getSourceClientOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getSourceClientOffset)(this.store.getState().dragOffset);\n    }\n    getDifferenceFromInitialOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getDifferenceFromInitialOffset)(this.store.getState().dragOffset);\n    }\n    constructor(store, registry){\n        this.store = store;\n        this.registry = registry;\n    }\n}\n\n//# sourceMappingURL=DragDropMonitorImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRegistryImpl: () => (/* binding */ HandlerRegistryImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/asap */ \"(ssr)/./node_modules/@react-dnd/asap/dist/index.js\");\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _contracts_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts.js */ \"(ssr)/./node_modules/dnd-core/dist/contracts.js\");\n/* harmony import */ var _interfaces_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interfaces.js */ \"(ssr)/./node_modules/dnd-core/dist/interfaces.js\");\n/* harmony import */ var _utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getNextUniqueId.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\");\n\n\n\n\n\n\nfunction getNextHandlerId(role) {\n    const id = (0,_utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__.getNextUniqueId)().toString();\n    switch(role){\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE:\n            return `S${id}`;\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET:\n            return `T${id}`;\n        default:\n            throw new Error(`Unknown Handler Role: ${role}`);\n    }\n}\nfunction parseRoleFromHandlerId(handlerId) {\n    switch(handlerId[0]){\n        case 'S':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n        case 'T':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n        default:\n            throw new Error(`Cannot parse handler ID: ${handlerId}`);\n    }\n}\nfunction mapContainsValue(map, searchValue) {\n    const entries = map.entries();\n    let isDone = false;\n    do {\n        const { done , value: [, value] ,  } = entries.next();\n        if (value === searchValue) {\n            return true;\n        }\n        isDone = !!done;\n    }while (!isDone)\n    return false;\n}\nclass HandlerRegistryImpl {\n    addSource(type, source) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateSourceContract)(source);\n        const sourceId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE, type, source);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addSource)(sourceId));\n        return sourceId;\n    }\n    addTarget(type, target) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type, true);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateTargetContract)(target);\n        const targetId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET, type, target);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addTarget)(targetId));\n        return targetId;\n    }\n    containsHandler(handler) {\n        return mapContainsValue(this.dragSources, handler) || mapContainsValue(this.dropTargets, handler);\n    }\n    getSource(sourceId, includePinned = false) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        const isPinned = includePinned && sourceId === this.pinnedSourceId;\n        const source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId);\n        return source;\n    }\n    getTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.dropTargets.get(targetId);\n    }\n    getSourceType(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        return this.types.get(sourceId);\n    }\n    getTargetType(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.types.get(targetId);\n    }\n    isSourceId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n    }\n    isTargetId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n    }\n    removeSource(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getSource(sourceId), 'Expected an existing source.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeSource)(sourceId));\n        (0,_react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__.asap)(()=>{\n            this.dragSources.delete(sourceId);\n            this.types.delete(sourceId);\n        });\n    }\n    removeTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getTarget(targetId), 'Expected an existing target.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeTarget)(targetId));\n        this.dropTargets.delete(targetId);\n        this.types.delete(targetId);\n    }\n    pinSource(sourceId) {\n        const source = this.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(source, 'Expected an existing source.');\n        this.pinnedSourceId = sourceId;\n        this.pinnedSource = source;\n    }\n    unpinSource() {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.pinnedSource, 'No source is pinned at the time.');\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n    }\n    addHandler(role, type, handler) {\n        const id = getNextHandlerId(role);\n        this.types.set(id, type);\n        if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE) {\n            this.dragSources.set(id, handler);\n        } else if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET) {\n            this.dropTargets.set(id, handler);\n        }\n        return id;\n    }\n    constructor(store){\n        this.types = new Map();\n        this.dragSources = new Map();\n        this.dropTargets = new Map();\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n        this.store = store;\n    }\n}\n\n//# sourceMappingURL=HandlerRegistryImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/contracts.js":
/*!*************************************************!*\
  !*** ./node_modules/dnd-core/dist/contracts.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSourceContract: () => (/* binding */ validateSourceContract),\n/* harmony export */   validateTargetContract: () => (/* binding */ validateTargetContract),\n/* harmony export */   validateType: () => (/* binding */ validateType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n\nfunction validateSourceContract(source) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.canDrag === 'function', 'Expected canDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.beginDrag === 'function', 'Expected beginDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.endDrag === 'function', 'Expected endDrag to be a function.');\n}\nfunction validateTargetContract(target) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.canDrop === 'function', 'Expected canDrop to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.hover === 'function', 'Expected hover to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.drop === 'function', 'Expected beginDrag to be a function.');\n}\nfunction validateType(type, allowArray) {\n    if (allowArray && Array.isArray(type)) {\n        type.forEach((t)=>validateType(t, false)\n        );\n        return;\n    }\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof type === 'string' || typeof type === 'symbol', allowArray ? 'Type can only be a string, a symbol, or an array of either.' : 'Type can only be a string or a symbol.');\n}\n\n//# sourceMappingURL=contracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/contracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/createDragDropManager.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDragDropManager: () => (/* binding */ createDragDropManager)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/es/redux.js\");\n/* harmony import */ var _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./classes/DragDropManagerImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\");\n/* harmony import */ var _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/DragDropMonitorImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\");\n/* harmony import */ var _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/HandlerRegistryImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\");\n/* harmony import */ var _reducers_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./reducers/index.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/index.js\");\n\n\n\n\n\nfunction createDragDropManager(backendFactory, globalContext = undefined, backendOptions = {}, debugMode = false) {\n    const store = makeStoreInstance(debugMode);\n    const monitor = new _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__.DragDropMonitorImpl(store, new _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__.HandlerRegistryImpl(store));\n    const manager = new _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__.DragDropManagerImpl(store, monitor);\n    const backend = backendFactory(manager, globalContext, backendOptions);\n    manager.receiveBackend(backend);\n    return manager;\n}\nfunction makeStoreInstance(debugMode) {\n    // TODO: if we ever make a react-native version of this,\n    // we'll need to consider how to pull off dev-tooling\n    const reduxDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__;\n    return (0,redux__WEBPACK_IMPORTED_MODULE_3__.createStore)(_reducers_index_js__WEBPACK_IMPORTED_MODULE_4__.reduce, debugMode && reduxDevTools && reduxDevTools({\n        name: 'dnd-core',\n        instanceId: 'dnd-core'\n    }));\n}\n\n//# sourceMappingURL=createDragDropManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/interfaces.js":
/*!**************************************************!*\
  !*** ./node_modules/dnd-core/dist/interfaces.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRole: () => (/* binding */ HandlerRole)\n/* harmony export */ });\nvar HandlerRole;\n(function(HandlerRole) {\n    HandlerRole[\"SOURCE\"] = \"SOURCE\";\n    HandlerRole[\"TARGET\"] = \"TARGET\";\n})(HandlerRole || (HandlerRole = {}));\n\n//# sourceMappingURL=interfaces.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9pbnRlcmZhY2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsa0NBQWtDOztBQUVuQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc3VhbHZpYmUtYXBwLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvaW50ZXJmYWNlcy5qcz80M2Q5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgSGFuZGxlclJvbGU7XG4oZnVuY3Rpb24oSGFuZGxlclJvbGUpIHtcbiAgICBIYW5kbGVyUm9sZVtcIlNPVVJDRVwiXSA9IFwiU09VUkNFXCI7XG4gICAgSGFuZGxlclJvbGVbXCJUQVJHRVRcIl0gPSBcIlRBUkdFVFwiO1xufSkoSGFuZGxlclJvbGUgfHwgKEhhbmRsZXJSb2xlID0ge30pKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJmYWNlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/interfaces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\n\n\n\n\nfunction reduce(// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_state = _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE, action) {\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.HOVER:\n            break;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_SOURCE:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.PUBLISH_DRAG_SOURCE:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.DROP:\n        default:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.ALL;\n    }\n    const { targetIds =[] , prevTargetIds =[]  } = action.payload;\n    const result = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.xor)(targetIds, prevTargetIds);\n    const didChange = result.length > 0 || !(0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_4__.areArraysEqual)(targetIds, prevTargetIds);\n    if (!didChange) {\n        return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n    }\n    // Check the target ids at the innermost position. If they are valid, add them\n    // to the result\n    const prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1];\n    const innermostTargetId = targetIds[targetIds.length - 1];\n    if (prevInnermostTargetId !== innermostTargetId) {\n        if (prevInnermostTargetId) {\n            result.push(prevInnermostTargetId);\n        }\n        if (innermostTargetId) {\n            result.push(innermostTargetId);\n        }\n    }\n    return result;\n}\n\n//# sourceMappingURL=dirtyHandlerIds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js":
/*!***********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOffset.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\nconst initialState = {\n    initialSourceClientOffset: null,\n    initialClientOffset: null,\n    clientOffset: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return {\n                initialSourceClientOffset: payload.sourceClientOffset,\n                initialClientOffset: payload.clientOffset,\n                clientOffset: payload.clientOffset\n            };\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            if ((0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_1__.areCoordsEqual)(state.clientOffset, payload.clientOffset)) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                clientOffset: payload.clientOffset\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOperation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nconst initialState = {\n    itemType: null,\n    item: null,\n    sourceId: null,\n    targetIds: [],\n    dropResult: null,\n    didDrop: false,\n    isSourcePublic: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return _objectSpread({}, state, {\n                itemType: payload.itemType,\n                item: payload.item,\n                sourceId: payload.sourceId,\n                isSourcePublic: payload.isSourcePublic,\n                dropResult: null,\n                didDrop: false\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE:\n            return _objectSpread({}, state, {\n                isSourcePublic: true\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            return _objectSpread({}, state, {\n                targetIds: payload.targetIds\n            });\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__.REMOVE_TARGET:\n            if (state.targetIds.indexOf(payload.targetId) === -1) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                targetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.without)(state.targetIds, payload.targetId)\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return _objectSpread({}, state, {\n                dropResult: payload.dropResult,\n                didDrop: true,\n                targetIds: []\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n            return _objectSpread({}, state, {\n                itemType: null,\n                item: null,\n                sourceId: null,\n                dropResult: null,\n                didDrop: false,\n                isSourcePublic: null,\n                targetIds: []\n            });\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOperation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/index.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dirtyHandlerIds.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\");\n/* harmony import */ var _dragOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dragOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\");\n/* harmony import */ var _dragOperation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dragOperation.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\");\n/* harmony import */ var _refCount_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./refCount.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\");\n/* harmony import */ var _stateId_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./stateId.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\n\n\n\nfunction reduce(state = {}, action) {\n    return {\n        dirtyHandlerIds: (0,_dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__.reduce)(state.dirtyHandlerIds, {\n            type: action.type,\n            payload: _objectSpread({}, action.payload, {\n                prevTargetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__.get)(state, 'dragOperation.targetIds', [])\n            })\n        }),\n        dragOffset: (0,_dragOffset_js__WEBPACK_IMPORTED_MODULE_2__.reduce)(state.dragOffset, action),\n        refCount: (0,_refCount_js__WEBPACK_IMPORTED_MODULE_3__.reduce)(state.refCount, action),\n        dragOperation: (0,_dragOperation_js__WEBPACK_IMPORTED_MODULE_4__.reduce)(state.dragOperation, action),\n        stateId: (0,_stateId_js__WEBPACK_IMPORTED_MODULE_5__.reduce)(state.stateId)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/refCount.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n\nfunction reduce(state = 0, action) {\n    switch(action.type){\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_TARGET:\n            return state + 1;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TARGET:\n            return state - 1;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=refCount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9yZWZDb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4RjtBQUN2RjtBQUNQO0FBQ0EsYUFBYSw0REFBVTtBQUN2QixhQUFhLDREQUFVO0FBQ3ZCO0FBQ0EsYUFBYSwrREFBYTtBQUMxQixhQUFhLCtEQUFhO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L3JlZHVjZXJzL3JlZkNvdW50LmpzPzg4NmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQUREX1NPVVJDRSwgQUREX1RBUkdFVCwgUkVNT1ZFX1NPVVJDRSwgUkVNT1ZFX1RBUkdFVCB9IGZyb20gJy4uL2FjdGlvbnMvcmVnaXN0cnkuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHJlZHVjZShzdGF0ZSA9IDAsIGFjdGlvbikge1xuICAgIHN3aXRjaChhY3Rpb24udHlwZSl7XG4gICAgICAgIGNhc2UgQUREX1NPVVJDRTpcbiAgICAgICAgY2FzZSBBRERfVEFSR0VUOlxuICAgICAgICAgICAgcmV0dXJuIHN0YXRlICsgMTtcbiAgICAgICAgY2FzZSBSRU1PVkVfU09VUkNFOlxuICAgICAgICBjYXNlIFJFTU9WRV9UQVJHRVQ6XG4gICAgICAgICAgICByZXR1cm4gc3RhdGUgLSAxO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIHN0YXRlO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVmQ291bnQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/stateId.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(state = 0) {\n    return state + 1;\n}\n\n//# sourceMappingURL=stateId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9zdGF0ZUlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L3JlZHVjZXJzL3N0YXRlSWQuanM/NjRhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gcmVkdWNlKHN0YXRlID0gMCkge1xuICAgIHJldHVybiBzdGF0ZSArIDE7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0YXRlSWQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/coords.js":
/*!****************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/coords.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   getDifferenceFromInitialOffset: () => (/* binding */ getDifferenceFromInitialOffset),\n/* harmony export */   getSourceClientOffset: () => (/* binding */ getSourceClientOffset),\n/* harmony export */   subtract: () => (/* binding */ subtract)\n/* harmony export */ });\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */ function add(a, b) {\n    return {\n        x: a.x + b.x,\n        y: a.y + b.y\n    };\n}\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */ function subtract(a, b) {\n    return {\n        x: a.x - b.x,\n        y: a.y - b.y\n    };\n}\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */ function getSourceClientOffset(state) {\n    const { clientOffset , initialClientOffset , initialSourceClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n        return null;\n    }\n    return subtract(add(clientOffset, initialSourceClientOffset), initialClientOffset);\n}\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */ function getDifferenceFromInitialOffset(state) {\n    const { clientOffset , initialClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset) {\n        return null;\n    }\n    return subtract(clientOffset, initialClientOffset);\n}\n\n//# sourceMappingURL=coords.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/coords.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js":
/*!*******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/dirtiness.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL: () => (/* binding */ ALL),\n/* harmony export */   NONE: () => (/* binding */ NONE),\n/* harmony export */   areDirty: () => (/* binding */ areDirty)\n/* harmony export */ });\n/* harmony import */ var _js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\nconst NONE = [];\nconst ALL = [];\nNONE.__IS_NONE__ = true;\nALL.__IS_ALL__ = true;\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */ function areDirty(dirtyIds, handlerIds) {\n    if (dirtyIds === NONE) {\n        return false;\n    }\n    if (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n        return true;\n    }\n    const commonIds = (0,_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.intersection)(handlerIds, dirtyIds);\n    return commonIds.length > 0;\n}\n\n//# sourceMappingURL=dirtiness.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9kaXJ0aW5lc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2QztBQUN0QztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFZO0FBQ2xDO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L3V0aWxzL2RpcnRpbmVzcy5qcz8zYzA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludGVyc2VjdGlvbiB9IGZyb20gJy4vanNfdXRpbHMuanMnO1xuZXhwb3J0IGNvbnN0IE5PTkUgPSBbXTtcbmV4cG9ydCBjb25zdCBBTEwgPSBbXTtcbk5PTkUuX19JU19OT05FX18gPSB0cnVlO1xuQUxMLl9fSVNfQUxMX18gPSB0cnVlO1xuLyoqXG4gKiBEZXRlcm1pbmVzIGlmIHRoZSBnaXZlbiBoYW5kbGVyIElEcyBhcmUgZGlydHkgb3Igbm90LlxuICpcbiAqIEBwYXJhbSBkaXJ0eUlkcyBUaGUgc2V0IG9mIGRpcnR5IGhhbmRsZXIgaWRzXG4gKiBAcGFyYW0gaGFuZGxlcklkcyBUaGUgc2V0IG9mIGhhbmRsZXIgaWRzIHRvIGNoZWNrXG4gKi8gZXhwb3J0IGZ1bmN0aW9uIGFyZURpcnR5KGRpcnR5SWRzLCBoYW5kbGVySWRzKSB7XG4gICAgaWYgKGRpcnR5SWRzID09PSBOT05FKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKGRpcnR5SWRzID09PSBBTEwgfHwgdHlwZW9mIGhhbmRsZXJJZHMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBjb25zdCBjb21tb25JZHMgPSBpbnRlcnNlY3Rpb24oaGFuZGxlcklkcywgZGlydHlJZHMpO1xuICAgIHJldHVybiBjb21tb25JZHMubGVuZ3RoID4gMDtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGlydGluZXNzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/equality.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/equality.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areArraysEqual: () => (/* binding */ areArraysEqual),\n/* harmony export */   areCoordsEqual: () => (/* binding */ areCoordsEqual),\n/* harmony export */   strictEquality: () => (/* binding */ strictEquality)\n/* harmony export */ });\nconst strictEquality = (a, b)=>a === b\n;\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */ function areCoordsEqual(offsetA, offsetB) {\n    if (!offsetA && !offsetB) {\n        return true;\n    } else if (!offsetA || !offsetB) {\n        return false;\n    } else {\n        return offsetA.x === offsetB.x && offsetA.y === offsetB.y;\n    }\n}\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */ function areArraysEqual(a, b, isEqual = strictEquality) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    for(let i = 0; i < a.length; ++i){\n        if (!isEqual(a[i], b[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\n//# sourceMappingURL=equality.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9lcXVhbGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsY0FBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L3V0aWxzL2VxdWFsaXR5LmpzPzg3N2UiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHN0cmljdEVxdWFsaXR5ID0gKGEsIGIpPT5hID09PSBiXG47XG4vKipcbiAqIERldGVybWluZSBpZiB0d28gY2FydGVzaWFuIGNvb3JkaW5hdGUgb2Zmc2V0cyBhcmUgZXF1YWxcbiAqIEBwYXJhbSBvZmZzZXRBXG4gKiBAcGFyYW0gb2Zmc2V0QlxuICovIGV4cG9ydCBmdW5jdGlvbiBhcmVDb29yZHNFcXVhbChvZmZzZXRBLCBvZmZzZXRCKSB7XG4gICAgaWYgKCFvZmZzZXRBICYmICFvZmZzZXRCKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gZWxzZSBpZiAoIW9mZnNldEEgfHwgIW9mZnNldEIpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBvZmZzZXRBLnggPT09IG9mZnNldEIueCAmJiBvZmZzZXRBLnkgPT09IG9mZnNldEIueTtcbiAgICB9XG59XG4vKipcbiAqIERldGVybWluZXMgaWYgdHdvIGFycmF5cyBvZiBpdGVtcyBhcmUgZXF1YWxcbiAqIEBwYXJhbSBhIFRoZSBmaXJzdCBhcnJheSBvZiBpdGVtc1xuICogQHBhcmFtIGIgVGhlIHNlY29uZCBhcnJheSBvZiBpdGVtc1xuICovIGV4cG9ydCBmdW5jdGlvbiBhcmVBcnJheXNFcXVhbChhLCBiLCBpc0VxdWFsID0gc3RyaWN0RXF1YWxpdHkpIHtcbiAgICBpZiAoYS5sZW5ndGggIT09IGIubGVuZ3RoKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZm9yKGxldCBpID0gMDsgaSA8IGEubGVuZ3RoOyArK2kpe1xuICAgICAgICBpZiAoIWlzRXF1YWwoYVtpXSwgYltpXSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXF1YWxpdHkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/equality.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/getNextUniqueId.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextUniqueId: () => (/* binding */ getNextUniqueId)\n/* harmony export */ });\nlet nextUniqueId = 0;\nfunction getNextUniqueId() {\n    return nextUniqueId++;\n}\n\n//# sourceMappingURL=getNextUniqueId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9nZXROZXh0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmlzdWFsdmliZS1hcHAvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9nZXROZXh0VW5pcXVlSWQuanM/ZjQyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgbmV4dFVuaXF1ZUlkID0gMDtcbmV4cG9ydCBmdW5jdGlvbiBnZXROZXh0VW5pcXVlSWQoKSB7XG4gICAgcmV0dXJuIG5leHRVbmlxdWVJZCsrO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXROZXh0VW5pcXVlSWQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/js_utils.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   without: () => (/* binding */ without),\n/* harmony export */   xor: () => (/* binding */ xor)\n/* harmony export */ });\n// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */ function get(obj, path, defaultValue) {\n    return path.split('.').reduce((a, c)=>a && a[c] ? a[c] : defaultValue || null\n    , obj);\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter((i)=>i !== item\n    );\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isString(input) {\n    return typeof input === 'string';\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isObject(input) {\n    return typeof input === 'object';\n}\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */ function xor(itemsA, itemsB) {\n    const map = new Map();\n    const insertItem = (item)=>{\n        map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n    };\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    const result = [];\n    map.forEach((count, key)=>{\n        if (count === 1) {\n            result.push(key);\n        }\n    });\n    return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */ function intersection(itemsA, itemsB) {\n    return itemsA.filter((t)=>itemsB.indexOf(t) > -1\n    );\n}\n\n//# sourceMappingURL=js_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/matchesType.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchesType: () => (/* binding */ matchesType)\n/* harmony export */ });\nfunction matchesType(targetType, draggedItemType) {\n    if (draggedItemType === null) {\n        return targetType === null;\n    }\n    return Array.isArray(targetType) ? targetType.some((t)=>t === draggedItemType\n    ) : targetType === draggedItemType;\n}\n\n//# sourceMappingURL=matchesType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9tYXRjaGVzVHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXN1YWx2aWJlLWFwcC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L3V0aWxzL21hdGNoZXNUeXBlLmpzPzBkMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIG1hdGNoZXNUeXBlKHRhcmdldFR5cGUsIGRyYWdnZWRJdGVtVHlwZSkge1xuICAgIGlmIChkcmFnZ2VkSXRlbVR5cGUgPT09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIHRhcmdldFR5cGUgPT09IG51bGw7XG4gICAgfVxuICAgIHJldHVybiBBcnJheS5pc0FycmF5KHRhcmdldFR5cGUpID8gdGFyZ2V0VHlwZS5zb21lKCh0KT0+dCA9PT0gZHJhZ2dlZEl0ZW1UeXBlXG4gICAgKSA6IHRhcmdldFR5cGUgPT09IGRyYWdnZWRJdGVtVHlwZTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWF0Y2hlc1R5cGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\n");

/***/ })

};
;